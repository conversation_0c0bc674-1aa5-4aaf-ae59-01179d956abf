%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% Plantilla para informe de práctica, DISC, UCN.
% Original por Felipe Narváez, versión modificada por Brian Keith.
% Compilar dos veces, por razones de que el compilador crea un archivo 
% para la tabla de contenido, para que este funcione compilar una vez más.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\documentclass[oneside,12pt, letterpaper, titlepage]{book}
% Esto es para poder escribir acentos directamente:
\usepackage[utf8x]{inputenc}
% Esto es para que el LaTeX sepa que el texto esta en español:
\usepackage[spanish,activeacute,es-lcroman]{babel}
% Paquetes de la AMS:
\usepackage{amsmath, amsthm, amsfonts}
%Otros paquetes necesarios.
%\usepackage{apacite}

\usepackage{graphicx}
\usepackage[tc]{titlepic}
\usepackage{fancyhdr}
\usepackage[T1]{fontenc}
\usepackage{titlesec}
\usepackage{float}
%Esto utiliza arial.
\renewcommand{\rmdefault}{phv} % Arial
\renewcommand{\sfdefault}{phv} % Arial
%\usepackage{pslatex}  %Esto utiliza la fuente Times Roman (Casi indistinguible de Times New Roman)
\usepackage[titletoc]{appendix} %Este paquete para los anexos.
\usepackage{setspace} %Para espacio de 1.5
\usepackage{listings}  %Para codigo
\usepackage{multirow} %Tablas con fusiones de fila.
\usepackage{rotating}
\usepackage[numbers]{natbib}
\usepackage[none]{hyphenat} % Evita que las palabras sean cortadas
% Corta los url demasiado largos
\usepackage{url}
\usepackage{breakurl}
\usepackage[breaklinks]{hyperref}
% Para el texto alrededor de la imagen
\usepackage{wrapfig}
\usepackage{subcaption} % para acomodar las imágenes
\usepackage{lscape} %para hoja horizontal
\usepackage[normalem]{ulem} % tachar texto


%--------------------------------------------------------------------------
% Defino un nuevo comando para agregar espacios de 20 puntos, para utilizar
% 20 espacios solo utilizar las palabras \hsp
\newcommand{\hsp}{\hspace{2pt}}
%--------------------------------------------------------------------------
% Defino el nuevo titulo para capítulos con números romanos, como por
% ejemplo:
% I Introducción.
\renewcommand{\thechapter}{\Roman{chapter}}
%--------------------------------------------------------------------------
% Defino el nuevo titulo para subsecciones de capítulos con números árabes
% como por ejemplo:
% 1.1 Antecedentes generales 
\renewcommand{\thesection}{\arabic{chapter}.\arabic{section}}
%--------------------------------------------------------------------------
\addto\captionsspanish{% Replace "english" with the language you use
  \renewcommand{\contentsname}%
    {TABLA DE CONTENIDO}%
}

%--------------------------------------------------------------------------
% Defino el nuevo titulo para figuras de capitulos con numeros arabes
% como por ejemplo:
% 1.1 Antecedentes generales 
\renewcommand{\thefigure}{\arabic{chapter}.\arabic{figure}}
%--------------------------------------------------------------------------
\renewcommand{\thetable}{\arabic{chapter}.\arabic{table}}
%-------------------------------------------------------------------------

\titleformat*{\section}{\fontsize{12}{12}\bfseries}
\titleformat*{\subsection}{\fontsize{12}{12}\bfseries}
\titleformat*{\subsubsection}{\fontsize{12}{12}\bfseries}


%--------------------------------------------------------------------------
%Margenes de pagina.
\usepackage[top=2.5cm, bottom=2.5cm, left=2.5cm, right=2.5cm]{geometry}
%--------------------------------------------------------------------------
%Que aparezca la bibliografía en el índice.
%\usepackage{tocbibind}
%--------------------------------------------------------------------------
%Numeros de pagina en posiciones correctas.
\usepackage{fancyhdr} 
\pagestyle{myheadings}
\renewcommand{\headrulewidth}{0pt}
\fancyhead[L]{}
\fancyhead[R]{\nouppercase{\thepage}}
\fancyfoot[C]{}
%--------------------------------------------------------------------------
\usepackage{afterpage}
\usepackage{textcase}
\usepackage{enumitem}
\setlist{nolistsep}
%Listas ordenadas
\usepackage{datatool}
\newcommand{\sortitem}[1]{%
  \DTLnewrow{list}% Create a new entry
  \DTLnewdbentry{list}{description}{#1}% Add entry as description
}
\newenvironment{sortedlist}{%
  \DTLifdbexists{list}{\DTLcleardb{list}}{\DTLnewdb{list}}% Create new/discard old list
}{%
  \DTLsort{description}{list}% Sort list
  \begin{itemize}[leftmargin=*,label={}]%
    \DTLforeach*{list}{\theDesc=description}{%
      \item \theDesc}% Print each item
  \end{itemize}%
}

%Modificaciones a la tabla de contenidos.
\makeatletter
\def\numberSpaceChapter #1{#1.\enskip}
% I added .\enskip because without these the dots and the space between the number and the title would disappear altogether.

%Esto se encarga que los capítulos tengan puntos en su tab leader.
\renewcommand*\l@chapter[2]{%
  \ifnum \c@tocdepth >\m@ne
    \addpenalty{-\@highpenalty}%
    \vskip 1.0em \@plus\p@
    \setlength\@tempdima{1.5em}%
    \begingroup
      \parindent \z@ \rightskip \@pnumwidth
      \parfillskip -\@pnumwidth
      \leavevmode \bfseries
      \advance\leftskip\@tempdima
      \hskip -\leftskip 
      \@pnum@font #1\nobreak
      \xleaders\hbox{$\m@th
        \mkern \@dotsep mu\hbox{.}\mkern \@dotsep
        mu$}\hfill%
      \nobreak\hb@xt@\@pnumwidth{\hss\@pnum@font #2}\par
      \penalty\@highpenalty
      %Esto le quita la negrita.
    \endgroup
  \fi}
%Capitulos...
\let\orig@chapter\@chapter
\def\@chapter[#1]#2{\ifnum \c@secnumdepth >\m@ne
                       \if@mainmatter
                         \refstepcounter{chapter}%
                         \typeout{\@chapapp\space\thechapter.}%
                         \addcontentsline{toc}{chapter}%
                                   {CAPÍTULO~\protect\numberline{\thechapter}#1}%
                       \else
                         \addcontentsline{toc}{chapter}{#1}%
                       \fi
                    \else
                      \addcontentsline{toc}{chapter}{#1}%
                    \fi
                    \chaptermark{#1}%
                    \addtocontents{lof}{\protect\addvspace{10\p@}}%
                    \addtocontents{lot}{\protect\addvspace{10\p@}}%
                    \if@twocolumn
                      \@topnewpage[\@makechapterhead{#2}]%
                    \else
                      \@makechapterhead{#2}%
                      \@afterheading
                    \fi}
%Esto renueva los comandos asociados a los otros elementos de la tabla de contenidos para que todo quede bien.
\renewcommand*\l@section{\@dottedtocline{1}{1.5em}{2.3em}}
\renewcommand*\l@subsection{\@dottedtocline{2}{3.8em}{3.2em}}
\renewcommand*\l@subsubsection{\@dottedtocline{3}{7.0em}{4.1em}}
\renewcommand*\l@paragraph{\@dottedtocline{4}{10em}{5em}}
\renewcommand*\l@subparagraph{\@dottedtocline{5}{12em}{6em}}
%Esto se encarga de quitarle la negrita a la frontmatter.
\g@addto@macro{\frontmatter}{\addtocontents{toc}{\protect\def\protect\@pnum@font{\normalfont}}}
\g@addto@macro{\mainmatter}{\addtocontents{toc}{\protect\def\protect\@pnum@font{\normalfont}}}
\g@addto@macro{\backmatter}{\addtocontents{toc}{\protect\def\protect\@pnum@font{\normalfont}}}
\makeatother

%You can change the depth to which section numbering occurs, so you can turn it off selectively. By default it is set to 2. If you only want parts, chapters, and sections numbered, not subsections or subsubsections etc., you can change the value of the secnumdepth counter using the \setcounter command, giving the depth level you wish:
\setcounter{secnumdepth}{3}
%A related counter is tocdepth, which specifies what depth to take the Table of Contents to. It can be reset in exactly the same way as secnumdepth. For example:
\setcounter{tocdepth}{3}


%--------------------------------------------------------------------------
% Comienza el documento
\begin{document}
\sloppy %Como \usepackage[none]{hyphenat} evita que las palabras se corten, esta línea realiza la corrección al texto justificado.

\renewcommand{\figurename}{Figura}
\renewcommand{\tablename}{Tabla}
\renewcommand{\listtablename}{ÍNDICE DE TABLAS}
\renewcommand{\listfigurename}{ÍNDICE DE FIGURAS}

\setlength{\parindent}{0pt}
\setlength{\parskip}{\baselineskip} 
\onehalfspace

\captionsetup{belowskip=-24pt}

\begin{titlepage}
\centering
\vspace*{-0.3in}
%--------------------------------------------------------------------------
%Imagen o logotipo de la UCN

\includegraphics[scale=0.35]{./images/u.jpg}\\
\vspace{0.07in}
{\fontsize{14}{14}\selectfont
\textbf{UNIVERSIDAD CATÓLICA DEL NORTE}\\}
{\fontsize{11}{11}\selectfont
\vspace*{0.07in}
FACULTAD DE INGENIERÍA Y CIENCIAS GEOLÓGICAS\\
\vspace*{0.07in}
DEPARTAMENTO DE INGENIERÍA DE SISTEMAS Y COMPUTACIÓN\\
\vspace*{0.07in}
MAGÍSTER EN INGENIERÍA INFORMÁTICA\\}
\vspace*{1.8in}
%--------------------------------------------------------------------------
% Titulo del documento
{\fontsize{14}{14}\bfseries MÉTODOS HÍBRIDOS DE MACHINE LEARNING PARA \\ 
\vspace*{0.07in}
EL ANÁLISIS DE SENTIMIENTOS MULTICLASE\\}
\vspace*{0.07in}
\vspace*{0.14in}
{\fontsize{12}{12}
\textbf{Anteproyecto de Tesis}\\
}

%Memoria para optar al grado de Licenciado en Ciencias de la Ingeniería y al Titulo de Ingeniero Civil en Computación e Informática\\

%--------------------------------------------------------------------------
\vspace*{1in}
%--------------------------------------------------------------------------
%Datos Personales y/o profesores, alineados a la derecha de la hoja
\begin{center}
%\setlength{\leftskip}{7.2cm}
\fontsize{12}{12}\selectfont
\textbf{BRIAN FELIPE KEITH NORAMBUENA}\\
\vspace*{0.1in}
Profesor Guía: Doctor Claudio Meneses Villegas\\
\end{center}

\vspace*{1.35in}

Antofagasta, Chile\\
\vspace*{0.05in}
\textbf{Marzo, 2016}\\

\end{titlepage}

%--------------------------------------------------------------------------
\titleformat{\chapter}%code
[hang]%shape
{\fontsize{12}{12}\bfseries\centering}%format
{\Roman{chapter}{.}\hsp}%label
{10pt}%sep
{\fontsize{12}{12}\bfseries}%before code
\titlespacing*{\chapter}{0cm}{-13.6pt}{0cm}[0cm]
%--------------------------------------------------------------------------
%Capítulos sin enumeración, número de pagina en romano
\frontmatter
\setcounter{page}{2}
%--------------------------------------------------------------------------
% Esta es la TABLA DE CONTENIDO.
\begin{spacing}{-2}
\tableofcontents
\end{spacing}

\addtocontents{toc}{\let\protect\numberline\protect\numberSpaceChapter}
\addtocontents{toc}{~\hfill\textbf{Página}\par}
%DES-COMENTAR LA LINEA SIGUIENTE SI ES QUE LA TABLA DE CONTENIDOS TIENE MAS DE UNA PAGINA.
%\addtocontents{toc}{\protect\afterpage{~\hfill\textbf{Página}\par\medskip}}
\thispagestyle{plain}

%--------------------------------------------------------------------------
% Esta es la Tabla de Figuras
\begin{spacing}{0}
\listoffigures % Índice de figuras
\end{spacing}
\addtocontents{lof}{~\hfill\textbf{Página}\par}
%\addcontentsline{toc}{chapter}{ÍNDICE DE FIGURAS} % para que aparezca en la tabla de contenidos

%--------------------------------------------------------------------------
% Esta es el índice de Tablas
\begin{spacing}{0}
\listoftables % índice de tablas
\end{spacing}
\addtocontents{lot}{~\hfill\textbf{Página}\par}
%\addcontentsline{toc}{chapter}{ÍNDICE DE TABLAS} % para que aparezca en el índice de contenidos

\chapter*{NOMENCLATURA}
\begin{sortedlist}
\sortitem{\textbf{NB}: Naïve Bayes \vspace{6pt}}
\sortitem{\textbf{SVM}: Support Vector Machine \vspace{6pt}}
\sortitem{\textbf{LSA}: Latent Semantic Analysis \vspace{6pt}}
\sortitem{\textbf{POS}: Part-Of-Speech \vspace{6pt}}
\sortitem{\textbf{RNA}: Red Neuronal Artificial \vspace{6pt}}
\sortitem{\textbf{GPU}: Graphical Processing Unit \vspace{6pt}}
\sortitem{\textbf{WWW}: World Wide Web \vspace{6pt}}
\sortitem{\textbf{ROC:} Receiver Operating Characteristics \vspace{6pt}}
\sortitem{\textbf{AUC:} Area Under the Curve \vspace{6pt}}
\end{sortedlist}

%\newpage
\chapter*{GLOSARIO}
\begin{sortedlist}
    \sortitem{\textbf{Minería de datos:} es la práctica de examinar bases de datos preexistente para generar nueva información y conocimiento. \vspace{6pt}}
    \sortitem{\textbf{Minería de textos:} es un área multidisciplinaria basada en la recuperación de información, minería de datos, aprendizaje automático, estadísticas y la lingüística computacional. \vspace{6pt}}
    \sortitem{\textbf{Minería de opiniones:} también conocida como análisis de sentimientos, se refiere al uso de procesamiento de lenguaje natural, análisis de texto y lingüística computacional para identificar y extraer información subjetiva desde materiales fuente. \vspace{6pt}}
    \sortitem{\textbf{Red social:} es una estructura social compuesta por un conjunto de actores (individuos u organizaciones) que están relacionados. A modo de ejemplo se tiene \textit{Twitter} y \textit{Facebook}.\vspace{3pt}}
    \sortitem{\textbf{Aprendizaje automático:} también conocido como \textit{machine learning}, es un tipo de inteligencia artificial que provee a los computadores la habilidad de aprender sin ser explícitamente programados. Se centra en el desarrollo de programas que puedan aprender por sí mismos y cambiar cuando se exponen a nuevos datos. \vspace{6pt}}
    \sortitem{\textbf{Procesamiento de lenguaje natural:} es un campo de las ciencias de la computación, inteligencia artificial y lingüística computacional que trata sobre las interacciones entre los computadores y los lenguajes humanos. \vspace{6pt}}
    \sortitem{\textbf{Stop words:} palabras comunes que no aportan información significativa en el texto, se suelen eliminar durante el preprocesamiento. Por ejemplo la palabra ``the'' en inglés. \vspace{6pt}}
\end{sortedlist}

%\newpage
\chapter*{RESUMEN}
La minería de opiniones es un campo que combina técnicas de las áreas de recuperación de información, procesamiento de lenguaje natural y minería de datos. Esta disciplina ha crecido considerablemente en la última década. Esta área de investigación pretende determinar los sentimientos, opiniones, emociones, entre otros aspectos que se expresan en un texto con respecto a algo o alguien. Realizar esta tarea requiere de la utilización de enfoques de procesamiento de lenguaje natural y técnicas de \textit{machine learning}. 

Publicaciones recientes evidencian una baja precisión en la clasificación multiclase de opiniones con respecto a los valores obtenidos en clasificación binaria. El desarrollo de métodos que se comporten adecuadamente en problemas multiclase dentro del contexto de la minería de opiniones presenta una serie desafíos.

Este trabajo de investigación pretende desarrollar nuevos enfoques de análisis de sentimientos multiclase utilizando métodos híbridos o recursivos de \textit{machine learning}. En este contexto, será necesario establecer una línea base conformada por los enfoques tradicionales utilizados ampliamente en esta área, tanto en los aspectos de representación de texto como en los algoritmos de clasificación propiamente tal. El enfoque diseñado debería obtener resultados competitivos en el análisis de sentimientos de más de dos clases en comparación con los resultados de la línea base en el dominio establecido.

%Palabras en inglés en cursiva.
%Utilizar tabla de al final de tesis de doctorado de tesis (solo la parte de opinion mining).

%\paragraph{Palabras claves}
%Minería de opiniones, análisis de sentimientos, métodos híbridos, aprendizaje profundo.
%--------------------------------------------------------------------------
%Capítulos normales con enumeración y con número de pagina árabes
\mainmatter
%--------------------------------------------------------------------------
% Defino el formato de titulo para el capítulo, numero romano y el titulo
% del capítulo separado con una linea de color gris, de esta forma:
% I | Introducción 
\titleformat{\chapter}[hang]
{\fontsize{14}{14}\bfseries}
{CAPÍTULO \Roman{chapter}{.}\hsp}{14pt}{\fontsize{14}{14}\bfseries}
\titlespacing{\chapter}{0cm}{-13.6pt}{0.21cm}[0cm]

\titleformat{\section}[hang]
{\fontsize{14}{14}\bfseries}
{\arabic{chapter}{.}\arabic{section}\hsp}{14pt}{\fontsize{14}{14}\bfseries}
\titlespacing{\section}{0cm}{-0.21cm}{-0.21cm}[0cm]

\titleformat{\subsection}[hang]
{\fontsize{12}{12}\bfseries}
{\arabic{chapter}{.}\arabic{section}{.}\arabic{subsection}\hsp}{12pt}{\fontsize{12}{12}\bfseries}
\titlespacing{\subsection}{0cm}{-0.21cm}{-0.21cm}[0cm] 
%--------------------------------------------------------------------------
% TIP
% Para las secciones pueden probar como
% \section[nombre corto]{Nombre largo}
% El nombre corto aparecerá en el índice de contenido
% el nombre largo aparecerá como titulo de la sección
% Este tip también sirve para el ambiente \chapter.
%--------------------------------------------------------------------------
\chapter[INTRODUCCIÓN]{INTRODUCCIÓN}
Las opiniones son centrales a casi todas las actividades humanas debido a que son influencias clave sobre el comportamiento de las personas. Cada vez que es necesario tomar una decisión, los humanos buscan saber las opiniones de otros. En el mundo real, empresas y organizaciones quieren saber las opiniones del público acerca de sus productos y servicios. Además, los mismos clientes desean saber las opiniones de los demás con respecto a un cierto producto antes de comprarlo. En el pasado, las personas buscaban las opiniones de sus amigos y familia, mientras que las organizaciones realizaban encuestas o grupos focales. No obstante, con el crecimiento explosivo de las redes sociales (tales como \textit{Twitter} o \textit{Facebook}), los individuos y organizaciones utilizan la información provista por estos medios para apoyar su proceso de toma de decisiones. En este contexto, es que surge el campo del análisis de sentimientos, también llamado minería de opiniones (en inglés, \textit{sentiment analysis} \& \textit{opinion mining}) \cite{liu2011web}.

El análisis de sentimientos se puede definir informalmente como el proceso por el que se determina si una frase o acto de habla contiene una opinión, positiva o negativa, sobre una entidad concreta o sobre un concepto. El análisis de sentimientos es un proceso complejo, debido a que intenta reconocer un patrón en un texto expresado en lenguaje natural, usualmente en un dominio específico. 

Algunos de los desafíos que conlleva el análisis de sentimientos tienen relación a la conformación y representación del corpus del dominio (i.e., la forma de obtener y representar el conjunto de datos), la extracción de opiniones, la determinación de la orientación de la opinión, entre otros. Entre las aplicaciones más recurrentes del análisis de sentimientos se encuentran las revisiones de productos o servicios ofertados en Internet, discusiones en foros, comentarios en blogs, y el análisis de opiniones de redes sociales (e.g. \textit{Twitter}). Aunque el análisis de sentimientos se relaciona fuertemente a las redes sociales, no está limitado a ellas.

El análisis de sentimientos, es un área relativamente reciente en el campo de la minería de datos. Existen varias técnicas para extraer, procesar y buscar información objetiva dentro de los textos, no obstante, existen componentes subjetivos que también son de interés. Estos elementos, que incluyen a opiniones, sentimientos, emociones, entre otros, son el foco del análisis de sentimientos. Esta disciplina engloba una gran cantidad de tareas, como la extracción de sentimientos, la clasificación de sentimientos, la detección de subjetividad, el resumen de opiniones, la detección de spam, entre otras más. Para realizar estas actividades de manera correcta, es necesario lidiar con varios desafíos, específicamente con la formalización de lo que significa una opinión. Para este propósito, se ha desarrollado una serie de formalismos y representaciones matemáticas que permiten expresar las opiniones \cite{liu2011web}.

El análisis de sentimientos es un área con muchas oportunidades de desarrollo, especialmente debido al gran crecimiento de la información disponible en la web, por ejemplo, en blogs, redes sociales, foros, entre otros. Una de las aplicaciones que se puede dar a la minería de opiniones corresponde a la evaluación de productos o servicios, mediante el análisis de las opiniones o críticas que entregan los usuarios al respecto. Esta aplicación es de gran importancia para las organizaciones, pues permite descubrir lo que las personas piensan y dicen acerca de una determinada marca \cite{liu2011web}.

El resto de este documento está organizado de la siguiente manera: en el capítulo II se entregan todos los fundamentos teóricos correspondientes a los métodos utilizados en este documento. El capítulo III muestra la motivación detrás de este problema de investigación, además hace referencia a la originalidad del trabajo propuesto, y finalmente se muestran los resultados esperados al desarrollar la tesis. El capítulo IV se centra en la revisión sistemática del estado del arte dentro del contexto del problema propuesto. El capítulo V resume las hipótesis sobre las que se construirá este trabajo de investigación. En el capítulo VI se especifican los objetivos, tanto el general como los específicos. En el capítulo VII se detalla la metodología de trabajo a seguir a lo largo del desarrollo del trabajo de tesis. Finalmente, el capítulo VIII detalla el plan temporal de trabajo, incluyendo los principales hitos y entregables considerados.

\chapter[MARCO TEÓRICO]{MARCO TEÓRICO}
\label{chapter:marco_teorico}
En este capítulo se muestra la base teórica de la investigación desde lo más general a lo más particular. En primer lugar, se introducen los principales conceptos teóricos del análisis de sentimientos, correspondiente a la disciplina científica en la que se enmarca la investigación. Luego se introducen diversos métodos de aprendizaje automático aplicados en la minería de opiniones. Finalmente, se explican los conceptos fundamentales del aprendizaje profundo y de los métodos híbridos y recursivos.

\section{Conceptos preliminares}
El análisis de sentimientos no es un problema simple, sino que es multifacético y requiere de la conjunción de varias disciplinas. El análisis de sentimientos se encuentra dentro del amplio abanico de la minería de datos, específicamente se relaciona con la minería de texto. Varios de los desafíos enfrentados en la minería de opiniones son comunes al espectro general de la minería de texto, pero algunas problemáticas son específicas a este dominio de trabajo. Dado lo anterior, se entregará primero una breve introducción a las disciplinas de minería de datos y minería de texto.

\subsection{Minería de datos y aprendizaje automático}
Hoy en día hay una gran cantidad de datos almacenados en muchas bases de datos diferentes. Existe mucha información y conocimiento contenido en estos datos, sin embargo, debido al volumen de datos disponibles es imposible realizar este proceso manualmente, y es por esto que se requiere del uso de técnicas de minería de datos y aprendizaje automático. El objetivo fundamental de la minería de datos es obtener conocimiento a partir de bases de datos preexistentes. 

Este trabajo se focaliza en un problema de clasificación de opiniones, uno de los posibles enfoques para esta tarea es la aplicación de algoritmos de aprendizaje automático. En los problemas de aprendizaje se tiene una entrada $X$, una salida $Y$, y se debe aprender una función desde la entrada a la salida, con el objetivo de generalizar correctamente a nuevas instancias \cite{kotsiantis2007supervised}. 

El objetivo del aprendizaje automático es programar computadores para que usen datos de ejemplo o experiencias pasadas para resolver un problema dado. El resultado de la aplicación del aprendizaje automático es una función o un algoritmo que responde al problema dado. Los enfoques de aprendizaje automático se pueden clasificar en problemas de aprendizaje supervisado, no supervisado y basado en instancias \cite{alpaydin2014introduction}.

\subsection{Minería de texto}
La cantidad de información textual disponible en formato electrónico hoy en día es masiva. El mayor ejemplo de este crecimiento es la \textit{World Wide Web} (WWW), que se estima provee acceso a, cuando menos, 3 terabytes de texto \cite{baghela2012text}. Dado esto, ha surgido el desafío de buscar patrones interesantes, tendencias e información potencial de interés dentro de este cuerpo de textos \cite{baeza2002searching}. La minería de textos se refiere generalmente al proceso de extraer información interesante y no trivial desde un texto no estructurado \cite{elmasri2007fundamentals}.

En general, la minería de datos trabaja con datos estructurados (por ejemplo, base de datos relacionales), pero los documentos de texto no son datos estructurados y además presentan una serie de características propias y especiales. Debido a esto, las técnicas comunes no son directamente aplicables a este tipo de datos \cite{baghela2012text}.  

La minería de texto comparte muchas características de la minería de datos clásica, aunque difiere de ciertos modos \cite{tan1999text}:
\begin{itemize}
\item Muchos algoritmos de descubrimiento de conocimiento definidos en el contexto de la minería de datos son irrelevantes o no se adecuan correctamente para ser aplicados en texto.
\item Ciertas tareas de minería, tal como el análisis de relación entre conceptos, son únicas para la minería de texto.
\item La forma no estructurada de un texto requiere de un preprocesamiento lingüístico especial para extraer las principales características del texto.
\end{itemize}

La mayoría de los objetivos de la minería de texto caen en alguna de estas categorías de operaciones: búsqueda y recuperación, categorización (clasificación supervisada), resumen, análisis de tendencias, análisis de asociaciones, visualización, entre otros \cite{gupta2009survey}. En el presente reporte se realizará un análisis de asociaciones mediante la construcción de reglas de asociación.

\subsection{Minería de opiniones y análisis de sentimientos}
El desarrollo efectivo de sistemas de minería de opiniones tiene un gran número de desafíos. Primero, es necesario identificar los contenidos en un texto, esta tarea no es trivial debido a la naturaleza del lenguaje, que posee un gran número de sutilezas semánticas que no están presentes en otros tipos de datos. Segundo, los sentimientos deben ser clasificados de alguna manera y así determinar la orientación de éstos, para ello existen diversas formas de abordar esta problemática \cite{Pang:2008:OMS:1454711.1454712}.

Una opinión puede ser simplemente definida como un sentimiento positivo o negativo, un punto de vista, una emoción o una apreciación sobre alguna entidad. Matemáticamente, una opinión está definida como una quíntupla ($e_{j}$, $a_{jk}$, $so_{ijkl}$, $h_{i}$, $t_{l}$) donde $e_{j}$ representa la entidad y $a_{jk}$ es el $k$-ésima característica de la entidad $e_{j}$. $so_{ijkl}$ es el valor del sentimiento de la opinión desde el punto de vista del emisor de la opinión $h_{i}$ sobre el aspecto $a_{jk}$ de la entidad $e_{j}$ en el tiempo $t_{l}$. Ese valor puede ser positivo, negativo o neutral, incluso puede ser definido un rango más detallado, por ejemplo diferentes niveles de intensidad \cite{liu2011web}. 

Además del sentimiento y la opinión, la subjetividad y la emoción son otros dos conceptos relacionados de importancia dentro del área de minería de opiniones. Una oración subjetiva puede expresar alguna sensación personal, un punto de vista o creencia, sin embargo, no implica necesariamente algún sentimiento. Los autores en \cite{DBLP:RaaijmakersK08} indican que una buena clasificación de la subjetividad puede asegurar una mejor clasificación de sentimientos e incluso este proceso es considerado aún más complejo entre distinguir sentimientos positivos, negativos o neutrales. Por otro lado, una emoción puede ser vista como una expresión de los propios pensamientos subjetivos de un individuo. Las emociones están relacionadas estrechamente con los sentimientos, de hecho, la manera de medir la fortaleza de una opinión está enlazada a la intensidad de ciertas emociones, tales como: el amor, el odio, la sorpresa, el enojo, la tristeza, entre otras.

El objetivo de la minería de opiniones es descubrir todas las opiniones de un documento $D$ en forma de quíntuplas ($e_{j}$, $a_{jk}$, $so_{ijkl}$, $h_{i}$, $t_{l}$). Para lograr este objetivo, se necesita ejecutar una serie de tareas. La primera tarea es extraer todas las entidades en $D$, donde cada entidad $e_{j}$ es única. La segunda tarea es extraer todos los aspectos de las entidades, donde cada aspecto $a_{jk}$ es único para la entidad $e_{j}$. La tercera tarea es extraer la opinión y el tiempo $t_{l}$ en que se escribió esa opinión. La cuarta tarea es determinar la orientación $so_{ijkl}$ de la opinión sobre un aspecto. La última tarea es generar una representación de todas las opiniones en el formato de quíntuplas. La dificultad radica en el hecho que no todas estas tareas pueden ser resueltas por completo \cite{liu2011web}. El proceso completo se puede visualizar en la Figura \ref{fig:opm_proc}.

\begin{figure}[H]
    \begin{center}
        \includegraphics[scale=0.65]{images/opm_proc}
        \caption[El proceso de minería de opiniones.]{El proceso de minería de opiniones (fuente: elaboración propia basada en descripción de \cite{liu2011web}).}
        \label{fig:opm_proc}
    \end{center}
\end{figure}

Se muestra a continuación un ejemplo en donde se identifican todas las quíntuplas de opinión encontradas en un texto. El propósito de éste es mostrar la complejidad del proceso de análisis de sentimientos. El ejemplo ha sido obtenido y traducido de \cite{liu2011web}, corresponde a una entrada en un \textit{blog} personal. Las oraciones han sido etiquetadas con números para facilitar su referencia.

``\textit{14 de enero de 2013: (1) Hoy me compré un celular Nokia y mi novia se compró un celular Motorola. (2) Nos llamamos cuando llegamos a casa. (3) La voz en mi celular no era muy clara, mucho peor que la de mi celular anterior. (4) La cámara era buena. (5) Mi novia estaba bastante feliz con su celular. (6) Yo quería un celular con buena calidad de voz. (7) Por lo que mi compra fue una total decepción. (8) Devolveré el celular mañana.}''

Las entidades presentes son el celular de marca Motorola y el celular de marca Nokia, esto no es fácil de identificar directamente en la práctica. Determinar que significa ``mi celular'' y que significa ``su celular'' en las oraciones (3) y (5) es aún más complejo. La oración (4) no menciona ningún teléfono y no tiene pronombres que indiquen a qué celular podría pertenecer la cámara. La oración (6) expresa una opinión que podría tomarse como positiva si se ve las palabras utilizadas (``buena calidad''), pero por supuesto, este no es el caso. En las oraciones (7) y (8), es difícil saber a qué se refiere ``mi compra'' y ``el celular''. El emisor de la opinión es en todas las oraciones el autor del blog, excepto en la oración (5), donde la emisora es ``mi novia''.

Teniendo en cuenta esto, las quíntuplas que se obtienen son las siguientes:

\begin{itemize}
\item (``Celular Nokia'', ``Calidad de Voz'', ``Negativa'', ``Autor del Blog'', ``14 de enero de 2013'').
\item (``Celular Nokia'', ``Cámara'', ``Positiva'', ``Autor del Blog'', ``14 de enero de 2013'').
\item (``Celular Motorola'', ``-'', ``Positiva'', ``Novia del Autor'', ``14 de enero de 2013'').
\end{itemize}

El guión en la tercera tupla indica que no se hace referencia a un atributo en particular, sino al celular en su totalidad.

Además de los aspectos lingüísticos, el análisis de sentimientos debe tomar en cuenta la calidad del texto analizado. Más aún, las personas cometen una serie de errores ortográficos y gramaticales. Un aspecto importante en la minería de opiniones es detectar sarcasmos e ironías. Esta es una tarea complicada en este campo de investigación, especialmente por la ausencia de acuerdos entre los investigadores en cómo el sarcasmo y la ironía tienen que ser formalmente definidos \cite{Reyes:2012:MOD:2364634.2364905}. 

Otra tarea importante es detectar spam u opiniones con contenido no confiable que puedan distorsionar el análisis \cite{liu2011web}. En algunos dominios, tal como el de revisión de artículos científicos el spam no es un aspecto a considerar, esto debido a que la naturaleza de dichas revisiones evita la aparición de este tipo de contenido.

En el artículo \cite{ravi2015survey} se presenta una categorización de las tareas y enfoques utilizados en análisis de sentimientos. Se muestra una versión traducida en la Figura \ref{fig:SentimentClassificationTechniques}.

\begin{figure}[H]
    \begin{center}
        \includegraphics[scale=0.65]{images/fields-sa}
        \caption[Tareas y enfoques del análisis de sentimientos.]{Tareas y enfoques del análisis de sentimientos (fuente: \cite{ravi2015survey}).}
        \label{fig:SentimentClassificationTechniques}
    \end{center}
\end{figure}

El trabajo propuesto en este anteproyecto corresponde a una tarea de clasificación de sentimientos, específicamente a la determinación de polaridad, y la técnica a utilizar para esta tarea corresponde a un enfoque híbrido.

\section{Métodos de aprendizaje automático}
Los métodos utilizados en el minado de opiniones están relacionados con la extracción de información, preprocesamiento de datos, manejo de lenguaje natural y métodos de \textit{machine learning} los cuales juegan un papel fundamental para determinar la orientación de las opiniones. La tarea de aprendizaje puede ser dividida en dos grandes enfoques: aprendizaje supervisado donde las clases son proporcionadas en los datos y aprendizaje no supervisado donde las clases son desconocidas y el algoritmo de aprendizaje necesita generar automáticamente los valores de clase.

A continuación se presenta el clasificador simple de Bayes (Naïve Bayes), las máquinas de soporte vectorial y se explican además los conceptos de redes neuronales clásicas.

\subsection{Naïve Bayes}
La estrategia de clasificación basada en Naïve Bayes es uno de los más simples e intuitivos. La lógica subyacente al método de Naïve Bayes es una de las propiedades del álgebra de probabilidades, el llamado teorema de Bayes (ecuación \ref{equation:BayesTheorem}). Este teorema juega un papel fundamental en el reconocimiento de patrones y \textit{machine learning} \cite{book:PatternRecognitionMachineLearning}. El teorema de Bayes expresa la probabilidad condicional de un evento aleatorio $A$ dado $B$ en términos de la distribución condicional del evento $B$ dado $A$ y la distribución de probabilidad marginal de sólo $A$, esta ecuación se deduce de la ley de multiplicación \cite{website:collins2013em}.

\begin{equation} \label{equation:BayesTheorem}
	P(A|B) = \frac{P(B|A)P(A)}{P(B)}
\end{equation}

El método de Naïve Bayes está basado en este teorema. El modelo utilizado por el clasificador corresponde a la probabilidad condicional $P(C|A_1,A_2,...,A_n)$. Donde $C$ es la variable de clase dependiente y los ${A_1,A_2,...,A_n}$ son los diferentes atributos predictores. La función clasificadora de Naïve Bayes, mostrada en la ecuación \ref{equation:BayesEqua6}, obtenida mediante la aplicación del teorema de Bayes y una serie de simplificaciones.

\begin{equation} \label{equation:BayesEqua6}
	classify(a_1,...,a_n) = \arg\max_c\biggr[ \, P(C=c) \prod_{i=1}^{n} P(A_i=a_i|C=c) \biggr] \,
\end{equation}

Los valores de cada probabilidad condicional $P(A_i=a_i|C=c)$ corresponden a estimaciones basadas en los datos empíricos sobre los que se esté trabajando. El objetivo del clasificador es determinar, del conjunto de posibles valores que puede tomar $C$, cuál es el más probable.  

El método de Naïve Bayes asume que todos los atributos son condicionalmente independientes, pero este supuesto es generalmente violado en la práctica. Por ejemplo, las palabras en un documento claramente no son independientes entre ellas. A pesar de esto, los investigadores han mostrado que se producen muy buenos modelos \cite{liu2011web}.

\subsection{Máquinas de soporte vectorial}
El enfoque de aprendizaje automático más utilizado corresponde a las máquinas de soporte vectorial (SVM por sus siglas en inglés). Este enfoque tiene una base teórica sólida y además empíricamente ha mostrado ser el clasificador más preciso para documentos de texto \cite{liu2011web}.

Intuitivamente, una SVM funciona encontrando una superficie que separa las clases, de tal forma que la separación sea lo máxima posible, en esencia, consiste en un problema de optimización. Esta superficie queda determinada por un conjunto de vectores, denominados los vectores de soporte \cite{liu2011web}.

En el caso que los datos sean linealmente separables, es posible encontrar este plano óptimo sin errores. No obstante, en la realidad es poco probable encontrar un conjunto de datos linealmente separable, y se debe admitir un cierto margen de error. Formalmente, para el caso de clasificación binaria, se busca resolver el problema de optimización mostrado en la ecuación (\ref{svm}) \cite{liu2011web}.
 
\begin{equation}
\begin{aligned}
& \underset{\mathbf{w}}{\text{minimizar}}
& & \frac{\langle \mathbf{w}, \mathbf{w}\rangle}{2} \\
& \text{sujeto a}
& & y_i(\langle \mathbf{w}, \mathbf{x_i}\rangle + b) \geq 1 - \xi_i, \; i = 1, \ldots, n\\
&
& & \xi_i \geq 0, \; i = 1, \ldots, n\\
\end{aligned}
\label{svm}
\end{equation}

Donde $\mathbf{w}, b, x_i, y_i, \xi_i$ corresponden, respectivamente, al vector normal que define el plano separador para las clases, el término de bias que define el intercepto del plano, el $i$-ésimo dato con su correspondiente clase (-1 ó 1), y una variable de holgura para permitir un margen de error para cada dato \cite{liu2011web}.

En caso que no sea viable utilizar un plano para separar las clases, es posible aplicar transformaciones que lleven el conjunto de datos desde un espacio a otro. Estas transformaciones son conocidas como \textit{kernels}, y tienen el objetivo de transformar los datos originales en un conjunto de datos linealmente separable. La ecuación mostrada anteriormente utiliza como base un \textit{kernel} lineal, que corresponde a no realizar transformaciones \cite{liu2011web}.

\subsection{Redes neuronales}
Las redes neuronales artificiales (RNA), como su nombre sugiere, tratan de imitar la red de neuronas del cerebro humano, donde el procesamiento se realiza gracias al trabajo conjunto de unidades más simples llamadas neuronas; las que al interrelacionarse entre sí contribuyen a formar topologías de red más complejas que permiten resolver problemas más elaborados. 

Estas redes se construyen a partir de capas de neuronas artificiales de modo que los datos fluyen desde una capa de entrada, pasando a través de una o más capas intermedias antes de alcanzar la capa de salida. En el contexto del aprendizaje supervisado, la red es entrenada operando sobre la diferencia entre la salida actual y la salida deseada (error de predicción) para cambiar la fuerza de las conexiones entre los nodos (pesos). Mediante iteraciones, los pesos son modificados hasta que el error de salida alcanza un nivel aceptable, este proceso se llama aprendizaje automático ya que la red ajusta los pesos de tal modo que los patrones de salida de entrenamiento son reproducidos \cite{billings2013nonlinear}.

Las redes neuronales son una de las técnicas más utilizadas en el área de la inteligencia artificial pues permiten el aprendizaje de funciones bastante complejas con resultados en general buenos de acuerdo a las métricas de evaluación. De forma resumida se les puede tratar como funciones no lineales que toman una entrada y retornan una salida de acuerdo a una serie de pesos que definen la función. Estos pesos son los parámetros de la red y son los que determinan su comportamiento.

Las redes neuronales poseen un nivel de entrada que recibe los datos a procesar y un nivel de salida que entrega el resultado de la función, entre ellos pueden existir varias capas ocultas. Estas redes pueden presentarse en diversas configuraciones dependiendo de la cantidad de niveles que tengan, cantidad de unidades ocultas por nivel y codificación de las salidas. Se debe notar que no existe un gran beneficio de agregar niveles ocultos en una red tradicional, y de hecho puede llegar a ser perjudicial \cite{hornik1989multilayer}. Se puede observar un ejemplo de topología de red en la Figura \ref{fig:rna-multi} obtenida de \cite{clevertask}.

\begin{figure}[H]
\centering
\includegraphics[scale = 0.6]{images/redes-neuronales.png}
\caption{Red neuronal multicapa.}
\label{fig:rna-multi}
\end{figure}

Las redes neuronales se entrenan encontrando los valores apropiados para sus pesos. El algoritmo de aprendizaje más utilizado para esto se llama \textit{backpropagation} (retropropagación). La idea detrás de este algoritmo es presentarle a la red un ejemplo con su resultado y luego evaluar la diferencia entre el resultado de la red y el resultado esperado. Este error se puede definir en función de los pesos y es posible utilizar esta función como base para realizar un procedimiento de descenso por gradiente con el objetivo de minimizar el error \cite{hecht1989theory}.

Las redes neuronales tienen una serie de ventajas: son conceptualmente simples, son fáciles de entrenar y usar, tienen excelentes propiedades de aproximación, su comportamiento es íntegro y tolerante (procesamiento local y paralelo), y además pueden producir resultados satisfactorios, especialmente para problemas definidos pobremente asociados al reconocimiento de patrones y clasificación \cite{billings2013nonlinear}.  

La mayoría de las críticas a los modelos de redes neuronales clásicos es que producen modelos opacos y difíciles de interpretar que generalmente no pueden ser analizados por humanos. Esto aumenta la dificultad de estudiar el comportamiento o calcular características dinámicas a partir del modelo. Las redes neuronales simplifican el modelamiento pero complican el modelo. Existen aplicaciones donde esto no es importante, por ejemplo, donde sólo se requiere una aproximación del comportamiento real \cite{billings2013nonlinear}.


\section{Procesamiento de lenguaje natural}
La minería de opiniones requiere del uso de técnicas de procesamiento de lenguaje natural. En esta sección se detalla las técnicas de preprocesamiento, representación y de etiquetado gramatical que se utilizan en los métodos de clasificación de polaridad.

\subsection{Preprocesamiento de texto}
En las tareas de minería de texto, y por extensión de minería de opiniones, es necesario realizar previamente una fase de preprocesamiento. La complejidad de esta etapa depende de las necesidades específicas del problema y de las técnicas a utilizar. El preprocesamiento de lenguaje natural tiene además una serie de problemáticas, debido a la complejidad inherente de la comunicación humana. El preprocesamiento en general consiste de las siguientes tareas \cite{mahgoub2008text}:
\begin{enumerate}
\item Conversión del texto a minúsculas.
\item Tokenización del texto.
\item Filtrado de \textit{stop words}.
\item Filtrado de \textit{tokens}.
\item Lematización (en inglés, \textit{stemming}).
\end{enumerate}

Con el propósito de obtener todas las palabras requeridas en el texto, se necesita primero realizar un proceso de tokenización, es decir, el documento se divide en un flujo de palabras mediante la eliminación de toda la puntuación y espacios redundantes. Una vez hecho esto se puede construir un diccionario con todas las palabras que contiene el documento. No obstante, la dimensión de este diccionario puede ser reducida aun más mediante filtros y el uso de métodos de \textit{stemming} \cite{hotho2005brief}. El filtro estándar que se aplica es la eliminación de \textit{stop words}, es decir, se remueven las palabras que no aportan información significativa, tales como artículos, conjunciones, preposiciones, entre otras \cite{baeza1992information}.

Los métodos de \textit{stemming} buscan construir la forma básica de las palabras (la raíz), por ejemplo, mediante la remoción de la letra ``s'' en los sustantivos plurales,  la terminación ``ing'' de verbos en gerundio (en inglés) y otros elementos similares. La raíz de una palabra agrupa de forma natural a todos los términos que tengan el mismo significado (o al menos muy similar). Al final de este proceso todas las palabras quedan representadas por su raíz. Uno de los métodos disponibles para esta tarea es el algoritmo de Porter \cite{porter1980algorithm} para la etapa de \textit{stemming}, este algoritmo está basado en reglas que iterativamente convierten las palabras a sus raíces.

\subsection{Representación del texto}
Tras la etapa de preprocesamiento es necesario elegir una representación para el texto. El enfoque más simple y conocido es el de bolsas de palabras, en el que se construye un diccionario en base a todos los documentos y cada texto está asociado a un vector de palabras. Agrupando todos estos vectores se obtiene una matriz (generalmente poco poblada) que define una representación numérica de los datos de entrada \cite{weiss2010fundamentals}. 

En cada entrada $v_{ij}$ del vector de palabras se encuentra un valor calculado en función de la frecuencia de la $j$-ésima palabra en el $i$-ésimo documento \cite{weiss2010fundamentals}.  El valor en cada entrada puede ser tan simple como un indicador binario de si la palabra está o no en el documento, contar la cantidad de ocurrencias o basarse en un esquema como TF-IDF. En este último, TF representa la frecuencia de cada término e IDF representa la inversa de la frecuencia de documentos. Este es el esquema más conocido \cite{liu2011web}.

Para la representación de información más compleja se requiere del cálculo de otras características en función de los datos en bruto (tales como el etiquetado gramatical que se explica en la siguiente subsección). Las características a utilizar deben ser diseñadas en función del dominio particular de trabajo. Otros enfoques utilizan árboles de análisis sintáctico para representar el texto mediante la aplicación de etiquetado gramatical y \textit{parsing} de dependencias \cite{vilares2015syntactic}. En el caso de aprendizaje profundo los datos suelen representarse mediante vectores de palabras en que los clasificadores aprenden sus propias características \cite{socher2014recursive}.

\subsection{Etiquetado gramatical}
Una vez que el texto está separado en tokens, el próximo paso usualmente es realizar un análisis morfosintáctico para identificar características, por ejemplo, su categoría gramatical. Este análisis se conoce con el nombre de \textit{Part-Of-Speech tagging} o etiquetado gramatical.

El método toma como entrada un texto en algún lenguaje dado y a través de la aplicación de un modelo asigna una categoría gramatical a las palabras de una oración, por ejemplo: sustantivo, verbo, adjetivo, entre otros. Por otra parte, cada categoría tiene sus propias características, por ejemplo, los verbos tienen características como el tiempo y la persona, los que no son aplicables a los sustantivos. Se debe observar que para aplicar esta técnica se omite el apartado de \textit{stemming} del preprocesamiento, pues realizarlo impediría obtener la estructura gramatical correcta.

La complejidad de esta tarea depende del lenguaje objetivo a analizar, por ejemplo, el lenguaje español tiene una mayor complejidad en cuanto a las conjugaciones de los verbos y a la existencia de los sujetos implícitos. Una de las decisiones que se deberá tomar durante el transcurso del proyecto de tesis es definir el lenguaje a utilizar para el desarrollo del trabajo.

El proceso de etiquetado gramatical tiene dos principales desafíos. Primero, hay que lidiar con la ambigüedad de las palabras lo cual depende del contexto de la oración que se está analizando. El segundo desafío es asignar una categoría gramatical a una palabra de la cual el sistema no tiene el conocimiento para hacerlo. Para resolver ambos problemas, típicamente se toma el contexto que existe alrededor de la palabra dentro de una oración y se selecciona el más probable. La categoría gramatical tiene una característica relevante. Una palabra que pertenezca al mismo grupo de palabras puede reemplazar a un \textit{token} que tiene la misma categoría gramatical sin afectar gramaticalmente la oración \cite{book:rodrigues2015advanced}.

Hay varias herramientas para determinar la categoría gramatical de las palabras en una oración, la mayoría de ellas lo hacen solamente para el idioma inglés. La librería \textit{Stanford Log-linear Part-Of-Speech Tagger} \cite{website:StanfordPOSTagger}, implementada en Java 8, proporciona modelos en seis diferentes idiomas, incluyendo el español.

La Figura \ref{fig:POSTaggingTreeExample} muestra una representación de la oración ``\textit{In page 2 in the first paragraph a typo mistake was found.}'', la cual es parte de un texto escrito en inglés. Este fragmento se tomó a modo de ejemplo para mostrar gráficamente el proceso de categorización gramatical realizado por la librería \textit{Stanford Log-linear Part-Of-Speech Tagger}.

\begin{figure}[H]
    \begin{center}
        \includegraphics[scale=0.50]{images/POSTaggingTreeExample}
        \caption[Representación de POS Tagging.]{Representación de POS Tagging (significado de las etiquetas obtenido de: \cite{marcus1993building}).}
        \label{fig:POSTaggingTreeExample}
    \end{center}
\end{figure}

\section{Aprendizaje profundo}
Como parte de esta propuesta se contempla la aplicación de modelos de aprendizaje profundo (o \textit{deep learning} en inglés, los términos se utilizarán indistintamente) para la clasificación de polaridad. En esta sección se explican los fundamentos básicos de \textit{deep learning} y sus ventajas con respecto a los enfoques tradicionales.

\subsection{Fundamentos de \textit{deep learning}}
Los métodos de aprendizaje profundo buscan aprender jerarquías de atributos de alto nivel de los datos a partir de atributos de bajo nivel, generando una cadena de abstracción que permite construir modelos complejos. Los algoritmos de \textit{deep learning} se inspiran en el funcionamiento del cerebro, que utiliza representaciones profundas (por ejemplo, las múltiples capas del sistema visual) \cite{bengio2009learning}. 

La mayoría de los algoritmos de aprendizaje automático utilizan representaciones superficiales para modelar los datos. Una representación superficial se caracteriza por una función de evaluación simple. En cambio, los enfoques de \textit{deep learning} utilizan combinaciones de complejos cálculos no lineales (por ejemplo, redes neuronales de múltiples niveles) que permiten representar funciones más complejas \cite{stollenga2011using}. 

Los algoritmos superficiales utilizan estas representaciones simplificadas de la realidad debido a que modelos simples evitan el sobreajuste a los datos. No obstante, se ha demostrado que el uso de modelos complejos no necesariamente conlleva a un sobreajuste y en muchos casos el intentar utilizar un modelo simple resulta teóricamente en una dificultad mayor \cite{stollenga2011using}.

Otra diferencia entre los enfoques tradicionales de \textit{machine learning} y \textit{deep learning} es que en los primeros un experto humano debe diseñar la representación de los datos (escoger los atributos y características que se utilizarán), mientras que en los enfoques de aprendizaje profundo el conjunto de características se infiere a partir de los datos mediante algún algoritmo, en varios niveles de complejidad y abstracción, además de la predicción final. Este enfoque puede entenderse como la unión entre el aprendizaje de representaciones y el aprendizaje automático \cite{socher2014recursive}.

\subsection{Ventajas de \textit{deep learning}}
Muchos modelos en el área del procesamiento de lenguajes naturales están basados en contar las ocurrencias de las palabras. No obstante, esta estrategia puede perjudicar la capacidad de generalización de los modelos, especialmente cuando aparecen palabras que no estaban en el conjunto de entrenamiento original. Otra problemática a la que se enfrentan estas representaciones es la  ``maldición de la dimensionalidad``. Debido a que la representación de las palabras sobre un vocabulario de gran tamaño se encuentra poco poblado, los modelos tienden a sobreajustarse a los datos. Las soluciones clásicas que se entregan a estos problemas consisten en diseñar manualmente el conjunto de características a utilizar u optimizar una función objetivo simple. Los modelos de \textit{deep learning} aplicados al lenguaje natural utilizan una representación de vectores de palabras distribuida en vez de contar las ocurrencias de las palabras. Esta representación permite obtener modelos más robustos y con mayor capacidad de generalización \cite{socher2014recursive}.

Las arquitecturas de redes neuronales profundas han existido por un largo tiempo. No obstante, durante mucho tiempo las redes neuronales superficiales que utilizaban características diseñadas manualmente superaban en rendimiento a las redes con arquitecturas profundas \cite{socher2014recursive}. Hasta que en el año 2006 se introdujo una nueva técnica para entrenar las redes neuronales profundas. Este nuevo procedimiento permitió el resurgimiento de este campo \cite{hinton2006reducing}.

\subsection{Implementación de arquitecturas profundas}
Tang et al. \cite{tang2015deep} sugieren una serie de buenas prácticas y recomendaciones para investigadores que están recién iniciándose en el trabajo con redes neuronales profundas. Existen diversas maneras para implementar una arquitectura neuronal profunda. Una de las posibles formas es calcular los gradientes de cada parámetro manualmente, y luego utilizar dicho valor para calcular cada parámetro. No obstante, este método no es escalable, pues en caso de cambiar la arquitectura los parámetros deben ser recalculados. Debido a que el objetivo es centrarse en la construcción de una arquitectura neuronal más poderosa que el cálculo de gradientes, los autores proponen que se utilice un enfoque de implementación por capas. De esta forma, cada capa de la arquitectura profunda es independiente de la otra y es posible entonces probar diferentes arquitecturas sin requerir grandes modificaciones ni cálculos manuales. 

Ejemplos de implementación por los mismos autores de \cite{tang2015deep} pueden ser encontrados en \url{http://ir.hit.edu.cn/~dytang}. También es posible utilizar librerías basadas en el uso de GPU's, tales como \textit{Theano} \url{deeplearning.net/software/theano/}, \textit{Torch} \url{torch.ch} y \textit{Caffe} \url{caffe.berkeleyvision.org/}. Otra herramienta que puede resultar útil es la librería recientemente publicada \textit{TensorFlow} de \textit{Google} (\url{https://www.tensorflow.org/}) que está enfocada en métodos de \textit{machine learning}, específicamente \textit{deep learning}.

\section{Métodos híbridos y recursivos}
En esta sección se muestran las definiciones de los métodos híbridos y los métodos recursivos. Éstos enfoques toman como base los elementos expuestos en las secciones anteriores. 

Tanto los métodos híbridos como los recursivos poseen la desventaja de tener una mayor complejidad con respecto a los enfoques tradicionales, tanto en el modelo subyacente como en la implementación. No obstante, permiten, en general, obtener mejores resultados en cuanto a la precisión de la clasificación \cite{ravi2015survey}. 

\subsection{Métodos híbridos} 
Se define como método híbrido aquel que combina los dos enfoques tradicionales, es decir, utiliza tanto técnicas de aprendizaje automático como técnicas basadas en la semántica del texto, haciendo uso de diccionarios de sentimientos \cite{yousef2014sentiment,ravi2015survey}.

Debido a la mayor complejidad computacional de los métodos híbridos no se utilizan tan frecuentemente como sus contrapartes tradicionales, aunque recientemente existe una tendencia a desarrollar nuevos enfoques híbridos  \cite{yousef2014sentiment}.

\subsection{Métodos recursivos} 
Se define como método recursivo aquel que utiliza variaciones o extensiones de las estructuras de redes neuronales recursivas (i.e. aprendizaje profundo). Estas redes permiten procesar lenguaje natural y modelar la estructura gramatical de las oraciones. La recursión surge de aplicar la misma red neuronal en cada nodo de la estructura gramatical \cite{socher2014recursive}.

Los métodos de aprendizaje profundo y en particular los enfoques recursivos han empezado a ser utilizados más frecuentemente en los trabajos de análisis de sentimientos, no obstante, la mayor parte del trabajo sigue siendo en enfoques tradicionales tales como SVM \cite{tang2015deep}.

\chapter[PLANTEAMIENTO DEL PROBLEMA]{PLANTEAMIENTO DEL PROBLEMA}
En este capítulo se presenta la motivación tras esta propuesta de investigación, se explica la relevancia del problema a abordar y la originalidad de la propuesta. Finalmente, se detallan las preguntas de investigación y los resultados esperados al finalizar ésta.

\section{Motivación}
La detección de polaridad es la tarea básica de la minería de opiniones. El objetivo principal en las aplicaciones de análisis de sentimientos es determinar la orientación de las opiniones emitidas, independientemente de las otras tareas particulares que pueden abordar dentro del abanico de posibilidades de la minería de opiniones.

La mayoría de los trabajos realizados sobre determinación de polaridad utilizan un enfoque de clasificación binaria \cite{ravi2015survey}. Otros estudios que realizan clasificación multiclase con cinco niveles (muy negativo, negativo, neutral, positivo y muy positivo) aplican métodos de regresión y luego transforman a la clase correspondiente \cite{liu2011web}. No obstante, trabajos recientes tales como \cite{socher2013recursive} ilustran que la clasificación multiclase de polaridad a nivel de documento completo sigue siendo un problema complejo, incluso con el enfoque de aprendizaje profundo propuesto en dicho artículo.

En particular, existe una serie de aplicaciones prácticas que se beneficiarían de una clasificación más precisa y detallada de las opiniones que el enfoque binario común adoptado por la mayoría de los estudios en esta área \cite{ravi2015survey}. En este contexto se detecta la necesidad de métodos que permitan una clasificación granular de la polaridad de las opiniones, sin caer en la pérdida de precisión mencionada anteriormente.

\section{Relevancia del problema}
El desarrollo de un método de clasificación de polaridad exitoso tiene una alta importancia en una gran cantidad de aplicaciones, tales como: análisis de mercado y política, predicción de éxito de películas, inteligencia de negocios, sistemas de recomendación, entre otras. La mayoría de las aplicaciones sólo utilizan enfoques de clasificación binaria. Las aplicaciones detalladas a continuación se beneficiarían de la mayor granularidad en la clasificación de opiniones, permitiendo análisis más detallados de las tendencias y resultados de mayor precisión \cite{ravi2015survey}. 

En términos de análisis de mercado y política, una de las aplicaciones del análisis de sentimientos permite predecir las fluctuaciones del mercado dentro del día mediante el análisis de la polaridad de una serie de noticias financieras \cite{li2014news} y el contenido de \textit{Twitter} \cite{bollen2011twitter}. En este mismo trabajo, se han aplicado también con éxito técnicas de minería de opiniones sobre \textit{Twitter} con respecto a las elecciones de Estados Unidos con resultados prometedores. Otros clasificadores y métodos híbridos pueden ser utilizados sobre los datos del mercado para obtener mejor precisión \cite{ravi2015survey}. 

En cuanto a la predicción del éxito financiero de las películas, se ha aplicado el análisis de sentimientos sobre entradas de blogs \cite{yu2012mining} o el contenido de \textit{Twitter} \cite{du2014box}. Con respecto a las aplicaciones en el campo de inteligencia de negocios, se puede destacar un modelo que estudia la subscripción a una revista y que toma como entrada los correos entre la compañía y sus clientes \cite{coussement2009improving}, o un estudio de la satisfacción del cliente en servicios de aplicaciones móviles \cite{kang2014review}.

Dentro del contexto de los sistemas de recomendación, mediante la aplicación de técnicas de detección de polaridad se ha clasificado usuarios según sus comentarios en pesimistas y optimistas. En base a estos resultados se ha logrado mejorar la precisión de los puntajes reales y mejorar el rendimiento del sistema de recomendación \cite{garcia2013pessimists}.

Finalmente, se debe notar que el análisis de sentimientos es una disciplina que todavía está en su infancia y es un campo muy fértil con alta utilidad práctica e interés académico, por lo que las eventuales aplicaciones de esta propuesta pueden llegar a ser mucho más amplias a medida que el campo alcance una mayor madurez \cite{ravi2015survey}.

%Tomar como linea base resultados obtenidos en dominio de revisiones de articulos cientificos. Agregar a la relevancia del problema.

\section{Originalidad}
Se ha mencionado en las secciones anteriores varios de los métodos utilizados en minería de opiniones, tanto los enfoques clásicos supervisados y no supervisados, además de los métodos de aprendizaje profundo. La idea de esta investigación es proponer un método híbrido y/o recursivo para el análisis de sentimientos multiclase. Al momento de preparar este documento de anteproyecto de tesis se realizó una revisión sistemática, no se han encontrado estudios con ideas similares a esta propuesta.

Si bien en varios artículos se puede observar el problema del análisis de sentimientos multiclase en cuanto a la disminución de precisión, no se han encontrado trabajos que aborden directamente este problema. Tampoco se han encontrado artículos que combinen explícitamente los métodos híbridos clásicos con \textit{deep learning} para el análisis de sentimiento multiclase.

\section{Preguntas de investigación}
%Tabla resumen con autores / resultados / enfoque / dataset.
En base a lo anterior, se plantean las siguientes preguntas de investigación.

\begin{enumerate}
\item ¿Es posible combinar enfoques híbridos de clasificación de sentimientos con enfoques recursivos de aprendizaje profundo?
\item ¿Es posible mejorar la precisión en la clasificación multiclase de sentimientos mediante la aplicación de enfoques híbridos y/o recursivos?
\item ¿Cuáles serían las ventajas y desventajas de estos modelos híbridos y/o recursivos con respecto a la línea base?
\end{enumerate}

\section{Resultados esperados}
Al término de esta investigación se espera obtener los siguientes resultados:

\begin{enumerate}
%Transferir un metodo de vision (escala de expresiones) a analisis de texto. Problemas homologos a este en otro campo/dominio.
\item La evaluación objetiva de los posibles enfoques a utilizar para el desarrollo del nuevo método a proponer.
\item El diseño de un método híbrido y/o recursivo que permita realizar análisis de sentimientos multiclase con un rendimiento competitivo con el estado del arte.
\item Una evaluación empírica del método diseñado con respecto a la línea base en el dominio definido.
\end{enumerate}

\chapter[ESTADO DEL ARTE]{ESTADO DEL ARTE}
En este capítulo se presentan los trabajos relacionados con esta propuesta de investigación, se resaltan las ventajas y desventajas de cada método estudiado, y se explica el aporte que supondrá esta investigación. 

Se empieza mostrando una tabla que resume los principales trabajos relacionados, a continuación se comienza a detallar cada referencia, empezando por los trabajos referentes a la clasificación de polaridad de sentimientos, luego se estudian casos realizados en español y enfoques para el desarrollo de léxicos y corpus. Finalmente, se concluye el capítulo con un resumen del aporte que supondría este trabajo.

\section{Visión general}
En la Tabla \ref{table:summary} se muestran los resultados que han tenido distintas investigaciones a lo largo del tiempo para la tarea de determinación de polaridad. Se detallan estas investigaciones en las siguientes secciones, dando especial énfasis en los métodos utilizados para clasificar la polaridad, el rendimiento obtenido y otros aspectos de interés en el contexto de este anteproyecto de tesis.

En la columna de enfoque se especifica la estrategia utilizada, ésta puede ser basada en \textit{machine learning} (ML), basada en léxico (L), híbrida (H) o basada en \textit{deep learning} (DL). En la columna de dominio se muestra sobre qué área se está trabajando, la mayoría de los trabajos se realizan sobre críticas a películas o \textit{Twitter}.

Los valores de la columna de resultados están en términos de \textit{accuracy} global salvo se indique lo contrario. Se han reportado los mejores resultados obtenidos en el trabajo correspondiente. En caso que un trabajo realice pruebas en varios conjuntos de datos o con distintas cantidades de clase se reportarán los resultados separados por un \textit{slash} (/) en el mismo orden.

La mayoría de la información se ha obtenido desde el trabajo de recopilación de los autores de \cite{ravi2015survey} y de \cite{tang2015deep}. El primer trabajo trata sobre minería de opiniones en general, mientras que el segundo se centra en los enfoques de \textit{deep learning} aplicados al campo de análisis de sentimientos.

\newpage
\begin{table}[H]
\centering
\vspace{1.cm}
\caption{Tabla resumen de trabajos relacionados.}
\label{table:summary}
\scalebox{0.85}{
\begin{tabular}{|c|c|c|p{3cm}|p{3cm}|p{2.2cm}|p{4cm}|}
\hline
\textbf{Año}                   & \textbf{Enfoque} & \textbf{Clases} &  \centering\textbf{Dominio}                                &  \centering\textbf{Método}                                            & \textbf{Resultados}      & \hspace{1.3cm}\textbf{Autores}          \\ \hline
\textbf{2002}                  & ML               & 2               & Críticas de películas                                          & NB, clasificación de entropía máxima, y SVM.               & 82.9\%         & Pang et al.               \\ \hline
\multirow{2}{*}{\textbf{2009}} & ML               & 3               & Críticas de productos en inglés, holandés y francés      & SVM, MNB, \textit{boosting} y arquitectura en cascada               & 83.30\% / 69.80\% / 67.68\% & Boiy et al.               \\ \cline{2-7} 
                               & L           & 2               & Críticas de películas y productos traducidos a español & SO-CAL en español                                          & 71.81\%                 & Brooke et al.             \\ \hline
\multirow{2}{*}{\textbf{2011}} & L           & 2               & Críticas de películas                                 & SO-CAL                                                     & 76.37\%                 & Taboada et al.            \\ \cline{2-7} 
                               & H          & 2               & \textit{Twitter}                                               & Prueba chi-cuadrado, SVM y léxico                          & 85.40\%                 & Zhang et al.              \\ \hline
\textbf{2012}                  & ML               & 3               & Comentarios en foros                                  & Variantes de clasificador multidimensional bayesiano       & 83.63\%                 & Ortigosa-Hernández et al. \\ \hline
\multirow{2}{*}{\textbf{2013}} & DL               & 2 / 5             & Críticas de películas                                 & RNTN                                                       & 85.40\% / 45.70\%         & Socher et al.             \\ \cline{2-7} 
                               & H          & 2 / 3             & Críticas de productos turísticos                       & Etiquetado gramatical y reglas de asociación               & 85.50\% / 75.50\%         & Marrese                   \\ \hline
\multirow{2}{*}{\textbf{2014}} & H          & 2               & Críticas de películas                                  & \textit{Sentic patterns}, SVM y ELM.                                & 86.21\%                 & Poria et al.              \\ \cline{2-7} 
                               & DL               & 2 / 3             & \textit{Twitter}                                               & \textit{Deep learning}                                              & 87.61\% / 70.40\%         & Tang et al.               \\ \hline
\multirow{5}{*}{\textbf{2015}} & ML               & 3               & Críticas en Japonés                                   & Campos aleatorios condicionales (CRF)                      & 89.30\% $F_1$           & Shi et al.                \\ \cline{2-7} 
                               & DL               & 5 / 10            & Críticas a productos y películas                      & RNN e información de usuarios                              & 60.80\% / 43.50\%         & Tang et al.               \\ \cline{2-7} 
                               & DL               & 5 / 10            & Críticas a productos y películas                      & \textit{Gated} RNN                                                  & 67.60\% / 45.30\%         & Tang et al.               \\ \cline{2-7} 
                               & DL               & 2               & Críticas a películas                                  & \textit{Sentiment}-RNN y RNTN paralelas                             & 86.50\%                 & Li et al.                 \\ \cline{2-7} 
                               & L           & 2               & Críticas a productos, hoteles y películas                & Etiquetado gramatical y \textit{parsing} de dependencias & 78.50\% / 80.11\% / 89.38\% & Vilares et al.            \\ \hline
\end{tabular}
}
\end{table}



\section{Enfoques tradicionales}
La tarea de clasificación de sentimientos tradicionalmente se puede realizar de dos maneras: supervisada y no supervisada basada en semántica. El éxito de estas técnicas depende principalmente de la extracción apropiada del conjunto de características utilizadas para detectar sentimientos. Las técnicas supervisadas más utilizadas son las máquinas de soporte vectorial y clasificadores simples de Bayes. Las soluciones de \textit{machine learning} involucran construir clasificadores a partir de una colección de documentos, donde cada texto es usualmente representado como una bolsa de palabras \cite{Pang:2002:TUS:1118693.1118704}. También, es común aplicar algunas técnicas de \textit{stemming} y la eliminación de \textit{stop words}. Generalmente, los clasificadores que tienen un buen desempeño en el dominio en el cual han sido entrenados, no lo tienen en otro dominio debido a que son altamente dependientes de los datos de entrenamiento utilizados \cite{Aue+Gamon:05a}.

Un análisis de sentimientos efectivo requiere no solamente considerar las palabras individualmente, también hay que tomar en consideración la construcción lingüística de la oración que está siendo analizada dado que puede cambiar en su totalidad el sentimiento expresado. A modo de ejemplo, considerar la oración: ``\textit{Los efectos especiales eran pésimos, pero la historia y la actuación fueron geniales}''. En este caso, las palabras clave son ``pésimos'' y ``geniales''. Sin considerar el contexto, se podría afirmar que la frase es neutral (considerando que contiene una palabra negativa y una positiva). No obstante, el hecho de que esté el conector ``pero'' cambia la orientación del texto, pues ahora se le está dando más énfasis al hecho que la actuación y la historia fueron geniales por sobre lo pésimo de los efectos especiales. Esto lleva a que ahora la frase tiene ahora una orientación más positiva que antes.

La forma usual de lidiar con estas construcciones es definir una heurística. Los autores en \cite{Pang:2002:TUS:1118693.1118704}, por ejemplo, asumen que el alcance de la negación incluye las palabras entre el negador y la primera puntuación después del término negativo. Los autores en \cite{Taboada:2011:LMS:2000517.2000518} utilizan la información generada a partir del proceso de etiquetado gramatical para identificar el alcance de la negación.

En \cite{ravi2015survey}, los autores revisan los trabajos realizados en el campo de análisis de sentimientos desde el año 2002 al año 2014 e indican que la mayoría de los trabajos que han sido realizados corresponden al área de clasificación de sentimientos, y específicamente determinación de polaridad. Además, indica que en detección de polaridad, los enfoques basados en \textit{machine learning} entregan mejores resultados que los enfoques basados en léxicos y los híbridos sobre el conjunto de datos de \cite{pang2004sentimental}. En cuanto a la elección de características se han utilizado varios tipos de métodos sintácticos, semánticos y estadísticos. Estos métodos han probado ser exitosos, no obstante, queda bastante trabajo por realizar en cuanto a determinación de polaridad. 

En el mismo artículo los autores indican que SVM es la técnica con mayor aplicación, debido a su capacidad para resolver problemas de alta dimensionalidad. Existen casos en que redes neuronales o aprendizaje bayesiano con \textit{boosting} han obtenido mejores resultados que SVM. En cuanto al \textit{kernel} de las SVM el más utilizado es el \textit{kernel} lineal, aunque existen trabajos que utilizan \textit{kernels} no lineales. El clasificador simple de Bayes sin modificaciones también tiene un buen rendimiento sobre la detección de polaridad en general. 

Además de los enfoques centrados en SVM, RNA y enfoques basados en léxicos; los autores encontraron que algunas técnicas de inteligencia artificial no han sido explotadas exhaustivamente, tales como minería de reglas de asociación, sistemas basados en reglas difusas, minería de reglas, computación evolutiva, análisis de conceptos formales, teoría de campos aleatorios condicionales y algoritmos de aprendizaje en línea. Además indican que el uso de ontologías puede tener varias aplicaciones en el campo. Se debe notar que el trabajo realizado por \cite{ravi2015survey} contiene pocas referencias con respecto a los enfoques de aprendizaje profundos que han surgido recientemente.

Zhang et al. \cite{zhang2011combining} proponen un enfoque que utiliza un método híbrido basado en un léxico específico de \textit{Twitter}. La propuesta se centra en clasificación binaria. El método propuesto supera la línea base entregada por los métodos de \textit{machine learning} puro o de léxico puro. Además, mediante la aplicación de la prueba chi-cuadrado se identifican algunos \textit{tweets} que anteriormente no podían ser detectados.

En el artículo \cite{ortigosa2012approaching} los autores abordan una problemática multidimensional, en el que utilizan tres dimensiones relacionadas para el análisis de sentimientos. La mayoría de los enfoques tradicionales están enfocados a un caso de una única dimensión y son inapropiados, mientras que los enfoques de \textit{multilabel} no pueden ser aplicados directamente. Dado esto, los autores proponen el uso de una red de clasificadores Bayesianos multidimensional. Además, aplicando técnicas semisupervisadas evitan el arduo trabajo de etiquetar manualmente los ejemplos.

En el artículo \cite{shi2015supervised} se propone un nuevo método para obtener la orientación semántica de las palabras y además se utiliza teoría de campos aleatorios condicionales para la determinación de polaridad de documentos en idioma chino en conjunto con técnicas de aprendizaje supervisado. A pesar de las dificultades inherentes del procesamiento de lenguaje natural, los resultados experimentales muestran que el nuevo enfoque es factible y efectivo. Se debe notar que el procesamiento de lenguaje chino es diferente al tratamiento que se da a textos en inglés o español.

\section{Aprendizaje profundo}
En el trabajo de recopilación \cite{tang2015deep} los autores indican que en los últimos años los enfoques de \textit{deep learning} han mejorado el estado del arte en varias de las tareas de la minería de opiniones, tales como la extracción y clasificación de opiniones y la construcción de léxicos. Los autores entregan una revisión de los enfoques más exitosos de aprendizaje profundo y los desafíos futuros de este campo. Se indica en este artículo que el método más utilizado para representar el texto es mediante el aprendizaje de ``\textit{word embeddings}`` (i.e. representaciones continuas de las palabras). El aprendizaje de la representación facilita el trabajo pues no es necesario realizar el diseño de las características que utilizará el clasificador como en los enfoques tradicionales. 

En \cite{socher2013recursive} proponen un enfoque de aprendizaje profundo utilizando redes neuronales recursivas tensoriales (RNTN), el nuevo enfoque supera a los métodos tradicionales (SVM y NB) y es mejor que otros enfoques de redes neuronales recursivas. Además, el modelo logra capturar adecuadamente la negación y la conjunción contrastiva en las opiniones. Una observación realizada en este trabajo es que durante el proceso de etiquetado manual de sus datos, los humanos encargados del proceso rara vez utilizaron las categorías extremas y la mayor parte de la variabilidad pudo ser capturada por cinco clases (negativa, algo negativa, neutral, algo positiva y positiva). Dado esto, para este trabajo se considerará la clasificación de polaridad entre tres y cinco clases.

En el artículo \cite{irsoy2014opinion} los autores exploraron la aplicación de redes neuronales recursivas profundas (mediante la concatenación de redes) para la tarea de extracción de opiniones. Evaluaron también su método con respecto a las redes superficiales con una única capa oculta y además lo compararon con otra propuesta propia anterior. Los autores reportan una mejora con respecto a las redes superficiales y su anterior propuesta. Si bien este trabajo no se centra en la determinación de polaridad de los sentimientos, sino en la extracción de las frases subjetivas, muestra la capacidad del aprendizaje profundo para la minería de opiniones. Además, los autores proponen utilizar otras nociones de profundidad ortogonales a la concatenación de clasificadores sugeridas en \cite{pascanu2013construct}.

Otro trabajo de clasificación de sentimientos basado en \textit{deep learning} se realiza en \cite{tang2014coooolll}, utilizando características diseñadas manualmente y un conjunto de características aprendidas a partir de diez millones de \textit{tweets} mediante la aplicación de una red neuronal con arquitectura profunda. El sistema obtuvo un alto rendimiento en la tarea de análisis de sentimientos en \textit{Twitter} de la conferencia anual \textit{SemEval 2014} \cite{rosenthal2014semeval}.

En el artículo \cite{tang2015learning} se discute la importancia de modelar información en las redes neuronales. En este trabajo de clasificación de sentimientos se incorporó un modelo tanto de los usuarios como de los productos. Para esta tarea se utiliza un modelo basado en espacios vectoriales, cuyas representaciones capturan características globales importantes que permiten determinar las preferencias de los usuarios y de los productos. Esta representación del texto entrega mejores resultados que la línea base con la cual fue comparada, correspondiente a modelos sin la información de los usuarios. 

En el artículo \cite{tang2015document}, otro trabajo de los mismos autores, proponen un modelo que aprende la representación del texto mediante la aplicación de redes neuronales convolucionales. En función de dicho aprendizaje, la semántica de cada oración y sus relaciones son codificadas adaptativamente en la representación del documento utilizando una red neuronal recurrente con compuertas. Esta representación revela que las redes neuronales recurrentes tradicionales son débiles en cuanto al modelamiento de composición de documentos, mientras que la adición de compuertas permite un aumento en el rendimiento de éstas.

En el artículo \cite{poria2014sentic} se propone un nuevo paradigma de análisis de sentimientos que mezcla aspectos de lingüística y \textit{machine learning} para mejorar la precisión de la detección de polaridad. El trabajo toma como línea base el comportamiento exhibido en el artículo \cite{socher2013recursive}, los autores reportan una mejora con respecto a estos resultados. El principal aporte de este trabajo es la aplicación de las llamadas ``\textit{sentic patterns}`` para modelar el análisis de sentimientos a un nivel de conceptos en vez del tradicional nivel de palabras.

En el trabajo \cite{li2015parallel} los autores desarrollan un enfoque de redes neuronales recursivas paralelas, en el que se propone el uso de una red neuronal recursiva tensorial en conjunto con una red neuronal recursiva tradicional. Esta arquitectura permite sacar provecho de la estructura de las etiquetas de sentimiento, pues las redes neuronales forman una estructura cooperativa. Los resultados reportados por los autores son competitivos con el estado del arte.

\section{Trabajos en el idioma español}
Las diferencias léxicas y gramaticales entre el español y el inglés pueden impactar en el desempeño de sistemas entrenados para un lenguaje en particular. Categorizar una opinión en positiva, negativa o neutral parece una tarea simple, sin embargo, es realmente una tarea compleja y especialmente cuando las opiniones están en idiomas diferentes. Los autores en \cite{Boiy:2009:MLA:1612979.1612986} han estudiado el impacto de las particularidades del inglés, alemán y francés. Existe una carencia general de sistemas de minado de opiniones en lenguajes que no sean el inglés \cite{chen2010ai}.

Hay que recalcar que la investigación en el área de minería de opiniones ha tenido un gran incremento en la última década, la mayoría del trabajo está enfocado a textos escritos en inglés. Dado esto, cabe destacar el trabajo propuesto en \cite{website:UChile:ME:DIAWOM} donde se desarrolla un sistema de minería de opiniones para identificar preferencias sobre productos turísticos en la región de Los Lagos, Chile. El conjunto de opiniones utilizado está en inglés, estas opiniones fueron extraídas del sitio web \textit{TripAdvisor}. El trabajo expuesto es interesante en términos de la investigación desarrollada y su aplicabilidad práctica.

Hay algunas investigaciones de minería de opiniones que se han enfocado en el idioma español. Uno de los más relevantes es el propuesto en \cite{BrookeTT09}, el cual utiliza un modelo basado en semántica que define una colección de diccionarios para calcular sentimientos. Otro estudio recientemente propuesto por \cite{Vilares:2015:LAD:2744723.2744729} describe un sistema de minería de opiniones que clasifica por tópico textos en español extraídos de \textit{Twitter} utilizando la estructura sintáctica de sus oraciones. Un enfoque similar es utilizado por los mismos autores en la propuesta del artículo \cite{vilares2015syntactic}, donde se muestra un sistema de clasificación de opiniones de críticas de películas en español utilizando \textit{parsing} de dependencias y etiquetado gramatical.

\section{Léxico y corpus}
Los métodos no supervisados basados en semántica hacen uso de diccionarios donde diferentes tipos de palabras son clasificadas de acuerdo a su orientación semántica \cite{Turney:2002:TUT:1073083.1073153}. A diferencia de los métodos tradicionales de \textit{machine learning}, los métodos no supervisados basados en semántica poseen una mayor independencia del dominio, aunque su desempeño puede variar de un dominio a otro. Hay dos subcategorías importantes de mencionar: basada en diccionario y basada en corpus. 

Las técnicas basadas en diccionarios utilizan un conjunto de términos iniciales que usualmente son recolectados de manera manual. Este conjunto crece mediante la búsqueda en un diccionario de sinónimos y antónimos. Un ejemplo de este tipo de diccionarios es \textit{WordNet} el cual fue utilizado para desarrollar \textit{SentiWordNet} \cite{baccianella2010sentiwordnet}. El principal inconveniente de este tipo de enfoques es su incapacidad para hacer frente a orientaciones específicas de un dominio y contexto. 

Las técnicas basadas en corpus surgen con el objetivo de proporcionar diccionarios para un dominio específico. Éstos son generados a partir de un conjunto de semillas de opinión que crece a través de la búsqueda de palabras relacionadas por medio de técnicas estadísticas o semánticas, tales como análisis semántico latente o simplemente por la frecuencia de ocurrencias de las palabras en la colección de documentos utilizados \cite{SerranoGuerrero201518}.

La definición del léxico a utilizar y determinar si será necesario o no construir un corpus propio son unas de las decisiones que deberán tomarse durante el transcurso de la investigación y dependen directamente del dominio donde se trabajará, el conjunto de datos y el idioma a utilizar. Se debe notar que estos últimos tres elementos serán definidos más tarde durante el transcurso del trabajo de tesis de acuerdo a la planificación establecida.

\section{Conjuntos de datos}
%Que tan dificil es cada conjunto para clasificar sentimientos.
El conjunto de datos sobre el que se trabajará es de vital importancia para construir el modelo de clasificación. Existen varios conjuntos comúnmente utilizados para comparar enfoques de clasificación de sentimientos en inglés, no obstante, en español la cantidad de recursos es más escasa. La elección del lenguaje a utilizar cae dentro de las decisiones que deberán tomarse posteriormente durante el transcurso de la investigación. Se muestran varios conjuntos de datos públicos en la Tabla \ref{table:common-datasets}, adaptada desde \cite{ravi2015survey}. La mayoría de éstos se encuentran en el lenguaje inglés, aunque algunos están en español. Todas las páginas han sido revisadas el día 7 de marzo del 2016. Los valores del estado del arte corresponden a los mejores valores de \textit{accuracy} reportados hasta la fecha en el conjunto de datos.

\begin{table}[h]
\centering
\vspace{1cm}
\caption{Conjuntos de datos públicos.}
\label{table:common-datasets}
\scalebox{0.8}{
\begin{tabular}{|p{5cm}|p{5cm}|p{4cm}|p{3.5cm}|}
\hline
\centering \textbf{Data set}                                     & \centering \textbf{Web resource}                                                & \centering \textbf{Type} & \hspace{0.1cm} \textbf{Results}                                                 \\ \hline
\textit{Movie Review Data v2.0}                                           & \url{http://www.cs.cornell.edu/people/pabo/movie-review-data/}            & Movie reviews   &   92.70\%                                       \\ \hline
\textit{Multi-Domain Sentiment Dataset (version 2.0)}                   & \url{https://www.cs.jhu.edu/~mdredze/datasets/sentiment/}                 & Multi-domain      &  88.70\%                                              \\ \hline
\textit{TripAdvisor (HOpinion)}                                                       & \url{http://clic.ub.edu/corpus/es/node/106}                               & Hotel reviews in Spanish   &  93.28\%       \\ \hline
HASH                                                         & \url{http://demeter.inf.ed.ac.uk}                                 & \textit{Tweets}    &  75\%                     \\ \hline
\textit{Opinion Mining, Sentiment Analysis, and Opinion Spam Detection} & \url{http://www2.cs.uic.edu/~liub/FBS/sentiment-analysis.html}            & Multi-domain                                  &   97\% (\textit{vaccum}) y 87\% (\textit{DVD player})           \\ \hline
\textit{TBOD, CineCorpus, ML-SentiCon}                                  & \url{http://www.lsi.us.es/~fermin/index.php/Datasets}                     & Multi-domain, movie reviews in Spanish \& multi-lingual  & 93.45\% \\ \hline
\textit{ISEAR}                                                          & \url{http://www.affective-sciences.org/system/files/webpage/ISEAR\_0.zip} & Text with multiple sentiments                           &    88.64\%  \\ \hline
\textit{Large Movie Review Dataset}                                     & \url{http://ai.stanford.edu/~amaas/data/sentiment/}                       & Movie reviews                                &  88.89\%         \\ \hline
\end{tabular}
}
%citar http://ai.stanford.edu/~amaas/papers/wvSent_acl2011.pdf
\end{table}

\newpage
\section{Aporte de este trabajo}
El análisis del estado del arte se puede resumir en los siguientes puntos:
\begin{itemize}
\item \textbf{Enfoques híbridos}: estos métodos son competitivos con respecto a los enfoques únicamente basados en \textit{machine learning} o basados en léxicos. No obstante, su capacidad se ve compensada por la mayor complejidad inherente de los modelos. Hay una gran cantidad de enfoques que no han sido explorados exhaustivamente.
\item \textbf{Representación del texto}: existen varios enfoques en cuanto a la representación que se puede utilizar para los datos textuales. El enfoque tradicional (bolsa de palabras o TF-IDF) no es capaz de detectar adecuadamente la información contextual de cada oración.
\item \textbf{Aprendizaje profundo}: los trabajos revisados indican que la aplicabilidad de enfoques profundos y recursivos permiten mejorar los niveles de precisión con respecto a los métodos tradicionales. Las redes neuronales recursivas han entregado buenos resultados y capacidad de modelar los fenómenos semánticos del lenguaje tales como la negación y las conjunciones contrastivas.
\item \textbf{Análisis de sentimientos multiclase}: la mayoría de los trabajos siguen centrándose en la clasificación binaria de la polaridad o en algunos casos ternaria (i.e. incluyendo la clase neutral). Los trabajos realizados sobre clasificación multiclase muestran que la precisión es peor con respecto al caso binario. Esto es un comportamiento común en los problemas de clasificación en minería de datos.
\end{itemize}

Tomando como referencia estos aspectos, el trabajo de tesis explorará enfoques híbridos y/o recursivos (e.g. \textit{deep learning}) de \textit{machine learning} para el análisis de sentimientos en un dominio a ser definido. Se debe establecer una línea base conformada por enfoques tradicionales utilizados ampliamente en esta área, tanto en el aspecto de representación del texto como en los algoritmos utilizados. En particular, se ha trabajado previamente con el dominio de revisiones (en español) de artículos científicos sometidos a una conferencia internacional \cite{fuenteskeith2015}. Los resultados obtenidos en dicho trabajo corresponderían a la línea base en dicho dominio, debido a que no se han encontrado publicaciones que centren en este dominio de trabajo.

El nuevo enfoque diseñado debe obtener resultados superiores en el análisis de sentimientos de más de dos clases (e.g. cinco clases: ++, +, 0, -, -{}-) cuando se compara con los resultados obtenidos por la línea base en el estado del arte, en el dominio establecido. 

\chapter[HIPÓTESIS DE TRABAJO]{HIPÓTESIS DE TRABAJO}
\label{chapter:hipotesis}
Habiéndose definido la problemática existente en los métodos actuales, se plantean a continuación las dos hipótesis del trabajo de investigación a realizar. Cada una de estas hipótesis debe someterse a pruebas experimentales que las confirmen o refuten.

\section{Hipótesis}
\begin{description}
    \item[$H_{1}$] Un enfoque híbrido y/o recursivo es competitivo con los métodos utilizados en el estado del arte para el análisis de sentimientos de opiniones con múltiples clases (>2).
    \item[$H_{2}$] Un enfoque híbrido y/o recursivo aplicado a un dominio determinado (e.g., revisiones de artículos científicos) es superior en rendimiento a los resultados registrados en el estado del arte para el análisis de sentimientos de opiniones con múltiples clases (>2).
\end{description}

\section{Verificación de la hipótesis}
Para realizar el proceso de pruebas de hipótesis se utilizarán métodos cuantitativos, específicamente se evaluará el rendimiento de los clasificadores construidos (tanto los de la línea base como la nueva propuesta) mediante el uso de métricas, tales como la $precision$, el $recall$ y la medida $F_{\beta}$.

Para determinar si los resultados del método diseñado son competitivos con los encontrados en el estado del arte se debe definir un criterio de evaluación. Específicamente, se utilizará como criterio si es que los valores en las distintas métricas se encuentran dentro de una desviación estándar del promedio de los métodos preexistentes.

En la sección de metodología se explica detalladamente cada una de las métricas a utilizar. En particular, se explica tanto su interpretación como el cálculo de la métrica en función de los resultados obtenidos.

\chapter[OBJETIVOS]{OBJETIVOS}
\label{chapter:objetivos}
Habiéndose definido las hipótesis del trabajo y teniendo en cuenta las problemáticas de los métodos actuales, se plantean el objetivo general y los objetivos específicos de la propuesta de investigación.

\section{Objetivo general}
El objetivo general de este trabajo es diseñar un método híbrido y/o recursivo para el análisis de sentimientos multiclase.

\section{Objetivos específicos}
En función del objetivo general propuesto se plantean los siguientes objetivos específicos para este trabajo de investigación:
\begin{enumerate}
\item Analizar métodos del estado del arte para el análisis de sentimientos.
\item Diseñar un algoritmo para el análisis de sentimientos con un enfoque híbrido y/o recursivo.
\item Evaluar el rendimiento del método propuesto sobre conjuntos de datos públicos y sobre un dominio específico.
\item Difundir los resultados en medios científicos.
\end{enumerate}

\chapter[METODOLOGÍA]{METODOLOGÍA}
\label{chapter:metodologia}
En este capítulo se detalla la metodología propuesta para este trabajo de investigación. En particular, se define el tipo de investigación, se especifican los materiales y métodos requeridos, se presentan las etapas de ésta, la forma en que se realizarán las pruebas y validación de las hipótesis, y finalmente los recursos necesarios.

\section{Tipo de investigación}
Esta investigación se basa en un método no experimental cuantitativo, específicamente, el diseño es transversal con un alcance descriptivo. El trabajo corresponde a una investigación cuantitativa por dos motivos. Primero, debido a que las hipótesis han sido planteadas antes de realizar la recolección y análisis de datos. Segundo, para la evaluación de las hipótesis se utilizarán métodos de análisis estadístico, en particular, se utilizará el nivel de precisión para la clasificación de sentimientos de opiniones. Luego, se generalizarán a toda la población los resultados obtenidos a partir de la muestra.

El enfoque es no experimental debido a que no se tomarán nuevas muestras para construir los modelos de clasificación de sentimientos, sino que más bien se optará por utilizar bases de datos propietarias preexistentes o públicas usadas anteriormente en otras investigaciones.

Finalmente, el trabajo se clasifica como descriptivo ya que pretende situar en una variable a un conjunto de textos, por ejemplo, describir la orientación de las opiniones emitidas, por medio del análisis de sentimientos.

\section{Materiales y métodos}
En cuanto a los conjuntos de datos a utilizar para el \textit{benchmarking} del método propuesto en el caso general, se contempla la aplicación de los \textit{datasets} públicos mencionados durante el análisis del estado del arte (hipótesis 1). Para el caso del conjunto de datos asociado a un dominio específico, se debe primero definir claramente cuál será este dominio antes de escoger el \textit{dataset} a utilizar (hipótesis 2).

Con respecto a los algoritmos y métodos que se utilizarán para el desarrollo de este trabajo de investigación, se tiene definido que cuando menos la línea base debe de contar con los métodos de \textit{machine learning} correspondientes al clasificador simple de Bayes y a las máquinas de soporte vectorial. Además, se utilizarán redes neuronales (tanto arquitecturas superficiales como profundas/recursivas) para la elaboración de la línea base y el diseño del nuevo método. También se ha considerado la aplicación de los métodos no supervisados, tales como el etiquetado gramatical y la clasificación basada en heurísticas. 

A medida que se vaya desarrollando el trabajo de investigación, se contempla la aplicación de otros métodos (como algunos mencionados en el estado del arte) que no han sido evaluados exhaustivamente en los trabajos previos.


\section{Etapas de la investigación}
La metodología de trabajo planteada se muestra en la Figura \ref{fig:metodologia} y consta de cinco etapas.

\begin{figure}[H]
    \begin{center}
        \includegraphics[scale=0.79]{images/Metodologia}
        \caption{Metodología de trabajo.}
        \label{fig:metodologia}
    \end{center}
\end{figure}

\vspace*{-12pt}

La propuesta contempla inicialmente una etapa de análisis sistemático de los métodos aplicados en el estado del arte, incluyendo tanto métodos tradicionales de \textit{machine learning}, enfoques basados en léxico, híbridos y enfoques de \textit{deep learning}. Como resultado de esta primera fase se obtendrá un documento de revisión sistemática que detalle los métodos estudiados. Este documento contendrá además la evaluación de los métodos del estado del arte.

En función del análisis realizado se diseñará el método híbrido y/o recursivo, siendo ésta la fase más crítica del trabajo de investigación. Se utilizará el documento de la fase anterior para guiar el diseño de la propuesta.

Tras la confección del diseño del método se procederá a la implementación del método en algún lenguaje de programación adecuado (e.g., C, Python o R). La elección del lenguaje y las librerías a utilizar dependerá de las necesidades específicas del método diseñado, y por ende estos elementos se definirán una vez el diseño del método haya sido completado.

Una vez el nuevo método esté diseñado e implementado, se procederá a evaluar su rendimiento sobre los conjuntos de datos públicos. En esta fase el enfoque propuesto debe obtener resultados competitivos con el estado del arte. A continuación, se evaluará el método sobre un conjunto de datos para un dominio específico, donde el objetivo es que supere los resultados del estado del arte.

Finalmente, una vez obtenidos todos los resultados y realizados los análisis correspondientes, se deben divulgar en medios científicos (revistas indexadas), además de elaborar el documento final de la tesis.

\section{Pruebas y validación}
En esta investigación se evaluará una serie de métodos para construir el modelo de determinación de polaridad, midiendo el rendimiento obtenido en la clasificación de sentimientos de las opiniones. Los indicadores de rendimiento a utilizar para comparar los métodos son $recall$, $precision$ y $F_{\beta}$ con $\beta=1$. 

Se utilizará un enfoque de ``\textit{cross validation}'' para la validación de los modelos. Esta técnica reduce la variabilidad de los resultados, debido a las múltiples iteraciones que realiza con diferentes particiones de los datos para entrenar y probar el clasificador. Luego, los resultados obtenidos en cada iteración son promediados para obtener el resultado final.

Se detallarán a continuación las diferentes métricas de evaluación a utilizar en este trabajo. Una vez definidas las medidas de rendimiento, se procederá a explicar un método gráfico complementario para visualizar el funcionamiento de los clasificadores desarrollados.

\subsection{Métricas de evaluación}
En función de los datos obtenidos en el desarrollo de los experimentos, se construirá una matriz de confusión que resumirá los resultados. Esta matriz facilitará el análisis necesario para determinar dónde se producen los errores de clasificación. Utilizando esta matriz se calcularán los indicadores de rendimiento necesarios para evaluar el funcionamiento del clasificador a implementar, específicamente la $accuracy$, el $recall$, la $precision$ y la medida $F_{\beta}$.

La matriz de confusión es una tabla que muestra la distribución de los errores por las distintas categorías. Se detallará la matriz de confusión para el caso de dos clases, siendo los conceptos directamente generalizables. La forma de la matriz es como la que se muestra en la Tabla \ref{table:ConfusionMatrix}.

\vspace{0.2in}
\begin{table}[H]
	\caption{Matriz de confusión.}
	\label{table:ConfusionMatrix}
	\begin{center}
		\begin{tabular}{cc|c|c|}
			\cline{3-4}
            &  & \multicolumn{2}{c|}{\textbf{Clase verdadera}} \\ \cline{3-4} 
            &  & \textit{Positivo}   & \textit{Negativo}   \\ \hline
			\multicolumn{1}{|c|}{{\textbf{Clase predicha}}} & \textit{Positivo} & $a$                  & $b$ \\ \cline{2-4} 
			\multicolumn{1}{|c|}{}                          & \textit{Negativo} & $c$                  & $d$ \\ \hline
		\end{tabular}
	\end{center}
\end{table}

\vspace{-0.4in}
Donde $a$ es el número de predicciones correctas para instancias positivas, $b$ es el número de predicciones incorrectas para instancias negativas, $c$ es el número de predicciones incorrectas para instancias positivas y $d$ es el número de predicciones correctas para instancias negativas.

El indicador más simple para evaluar el rendimiento de un clasificador es la $accuracy$, correspondiente a la razón de ejemplos clasificados correctamente sobre el total de ejemplos en el conjunto de datos. Este indicador se puede calcular en función de los datos de la matriz de confusión según la ecuación \ref{equation:Accuracy} (se asume que el conjunto de datos no está vacío).

\begin{equation} \label{equation:Accuracy}
	accuracy = \frac{a + d}{a + b + c + d}
\end{equation}

Los otros indicadores, es decir, la $precision$ y el $recall$, son entendidos como medidas de relevancia. La $precision$ es la proporción de verdaderos positivos entre los elementos predichos como positivos. Conceptualmente, la $precision$ se refiere a la dispersión del conjunto de valores obtenidos de mediciones repetidas de una magnitud. Específicamente, un alto valor de $precision$ implica una baja dispersión en las mediciones. El $recall$ es la proporción de verdaderos positivos predichos de entre todos los elementos catalogados como positivos, esto es, la fracción de instancias relevantes que han sido clasificadas. La $precision$ y el $recall$ se calculan según la ecuación \ref{equation:PrecisionRecall} (se asume $a + b \neq 0$ y $a + c \neq 0$, respectivamente). 

\begin{equation} \label{equation:PrecisionRecall}
	precision = \frac{a}{a + b} \qquad	recall = \frac{a}{a + c}
\end{equation}

El indicador $F_{\beta}$ se considera como una media armónica ponderada (de acuerdo al parámetro $\beta$) que combina los valores de $precision$ y de $recall$ según la ecuación \ref{equation:FBeta}. Si $\beta$ es igual a uno, se está asignando la misma ponderación (o importancia) a la $precision$ que al $recall$, si $\beta$ es mayor que uno se está dando más importancia al $recall$, mientras que si es menor que uno se está dando más importancia a la $precision$ que al $recall$.
 
\begin{equation} \label{equation:FBeta}
	F_{\beta} = (1 + \beta^{2}) \times \frac{precision \times recall}{(\beta^{2} \times precision) + recall}
\end{equation}

\subsection{Curvas ROC}
Con el propósito de complementar la información entregada por los indicadores descritos anteriormente, se propone el uso de análisis de curvas ROC (\textit{Receiver Operating Characteristics}). Las curvas ROC son una representación gráfica del rendimiento de un clasificador \cite{fawcett2006introduction}. 

El método de las curvas ROC permite visualizar gráficamente el comportamiento de los clasificadores y compararlos mediante una serie de análisis de las curvas obtenidas. A continuación se describe brevemente el método para el caso binario. No se mostrará en detalle el caso multiclase, pues es más complejo y existen varias alternativas para abordarlo \cite{fawcett2006introduction}. 

La Figura \ref{fig:ROC} muestra un ejemplo de varias curvas ROC. El gráfico tiene en el eje de las abscisas la tasa de falsos positivos (falsas alarmas) y en el eje de las ordenadas la tasa de verdaderos positivos (aciertos). La diagonal que divide el espacio corresponde al rendimiento obtenido por un clasificador que realice una elección aleatoria. Si la curva ROC de un clasificador está en la región inferior a la recta del clasificador aleatorio, entonces dicho clasificador tiene un menor rendimiento que una elección aleatoria. Por otra parte, un clasificador que siempre asigna la clase positiva estaría asociado al punto $(1,1)$ y otro que siempre asigna la clase negativa estaría asociado al origen $(0,0)$. Finalmente, se muestra además la curva generada por un clasificador perfecto, correspondiente a la recta que pasa por $(0,1)$ paralela al eje de las abscisas \cite{fawcett2006introduction}.

\begin{figure}[H]
    \begin{center}
        \includegraphics[scale=1]{images/ROC.png}
        \caption{Ejemplos de curvas ROC.}
        \label{fig:ROC}
    \end{center}
\end{figure}

En el gráfico de la Figura \ref{fig:ROC} se observan dos clasificadores a modo de ejemplo, uno considerado de buena calidad y otro de mala calidad con respecto al anterior. Idealmente, la curva generada por un clasificador será convexa y se aproximará a la forma del clasificador perfecto. Una métrica complementaria al análisis de las curvas ROC es el área bajo la curva (AUC, \textit{Area Under the Curve}). Este indicador permite resumir los resultados entregados por el gráfico en un valor  fácil de interpretar. El valor ideal corresponde a un área bajo la curva de 1.0 (i.e. un clasificador perfecto), y el valor correspondiente a una elección aleatoria es de 0.5. Cualquier valor bajo 0.5 es inaceptable pues su rendimiento es inferior a una elección aleatoria \cite{fawcett2006introduction}. Notar que en el ejemplo, el clasificador de buena calidad tiene un mayor valor de AUC que el clasificador de mala calidad.

Finalmente, se debe destacar que el concepto de las curvas ROC no es fácil de generalizar a problemas multiclase y existen diversos enfoques para realizar esta generalización. El método más simple y el más utilizado corresponde a realizar un análisis de una clase contra toda las otras (e.g., una clase contra las otras cuatro). Este método permite evaluar cómo se comporta el clasificador para cada una de las clases. Esta forma de aplicar el método de las curvas ROC a clasificadores multiclase tiene la ventaja que permite utilizar las mismas técnicas de análisis del caso binario \cite{fawcett2006introduction}.

\section{Recursos necesarios}
Se detallan a continuación los recursos necesarios para la investigación propuesta.

\subsection{Humanos}
\begin{itemize}
\item Alumno tesista de postgrado: Brian Keith Norambuena.
\item Profesor guía: Claudio Meneses Villegas.
\item Profesor corrector.
\end{itemize}

\subsection{Bibliográficos}
\begin{itemize}
\item Portal virtual de biblioteca UCN (Universidad Católica del Norte).
\item Bibliotecas digitales.
\end{itemize}

En caso que exista una referencia que no pueda ser obtenida mediante el portal UCN, entonces se utilizarán otras bibliotecas digitales que puedan tener una copia de la versión completa de dicha referencia. En caso de no ser posible encontrarla, se solicitará al profesor guía realizar el pedido correspondiente a la biblioteca de la universidad.

\subsection{Hardware}
Se requiere un computador que cumpla con las siguientes características mínimas:

\begin{itemize}
\item Procesador de 2.5 GHz.
\item Memoria RAM de 6 GB.
\item Sistema Operativo Windows 7 de 64 bits o superior.
\item Resolución de pantalla de 1024x768.
\item Capacidad de almacenamiento de 400 GB.
\end{itemize}


\subsection{Software}
En cuanto al software requerido para este trabajo se contempla como mínimo:
\begin{itemize}
\item Herramientas de ofimática.
\item Procesador de lenguaje LaTeX.
\item Navegador web.
\item Librerías de software auxiliares para desarrollo.
\end{itemize}

En caso que en el transcurso del trabajo de tesis se identifique un requerimiento adicional (e.g., librería pagada) se deberá realizar la solicitud correspondiente. Se estima que todo el trabajo podrá ser desarrollado recurriendo a software de código abierto o gratuito.

\subsection{Conjuntos de datos}
En cuanto a los conjuntos de datos a utilizar, se contempla el uso de alguno de los conjuntos de datos públicos mostrados en la Tabla \ref{table:common-datasets} para la evaluación general del método. Dependiendo del dominio de trabajo que finalmente se utilice para la evaluación del método se escogerá un conjunto de datos apropiado.  

\chapter[PLAN DE TRABAJO]{PLAN DE TRABAJO}
\label{chapter:plan}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
En este capítulo se presentan las actividades que conforman el plan de trabajo, desglosando las fases especificadas en el capítulo de metodología. 

\section{Planificación}
Se ha tomado como fecha de inicio del trabajo el 4 de abril de 2016, y además se han considerado los hitos mostrados en la Tabla \ref{table:hitos}:

\vspace{0.6cm}
\begin{table}[h]
\centering
\caption{Hitos del proyecto de tesis.}
\label{table:hitos}
\begin{tabular}{|c|p{9.5cm}|c|}
\hline
\textbf{\#} & \centering\textbf{Hito}                                                         & \textbf{Fecha} \\ \hline
1           & Entrega de documento de revisión sistemática y evaluación de métodos. & 24/05/2016     \\ \hline
2           & Entrega de documento de diseño del método.                            & 08/08/2016     \\ \hline
3           & Entrega de método implementado y documentación.                       & 01/09/2016     \\ \hline
4           & Entrega de documento de análisis de resultados.                       & 05/11/2016     \\ \hline
5           & Entrega de primer avance de tesis.                                    & 25/11/2016     \\ \hline
6           & Entrega de documento de tesis y artículo.                             & 15/12/2016     \\ \hline
\end{tabular}
\end{table}

El trabajo de tesis cuenta con dos decisiones críticas durante su desarrollo, enumeradas a continuación:
\begin{itemize}
\item \textbf{Dominio de trabajo}: Esta decisión corresponde a definir cuál será el dominio de trabajo particular sobre el que se planteará la segunda hipótesis del trabajo (el método propuesto es mejor que el estado del arte en dicho dominio), dado que la comprobación de dicha hipótesis depende del dominio elegido, se hace necesario escoger de tal modo que se aprovechen las fortalezas del método que se diseñará.
\item \textbf{Lenguaje de programación}: Esta decisión corresponde a definir el lenguaje de programación que se utilizará para implementar el método, pues esto determina las librerías y herramientas de desarrollo que estarán a disposición del investigador.
\end{itemize}

En cuanto al detalle de la planificación temporal y la estructura de las actividades a realizar (tiempos y prerrequisitos) se puede ver la carta Gantt en la Figura \ref{fig:crono}. Notar que si bien existe una fase explícita de análisis del estado del arte, este trabajo debe realizarse de manera continua. Se debe recalcar además que las fases referentes al diseño del método y a la implementación del mismo son las que requerirán la mayor cantidad de tiempo y son consideradas las fases críticas de la investigación.

\begin{landscape}
\section{Cronograma de actividades}
En la Figura \ref{fig:crono} se presenta la carta Gantt con el cronograma de actividades de este trabajo de investigación.
\begin{figure}[H]
\centering
\includegraphics[scale=.45]{images/cronograma}
\caption{Cronograma de actividades.}
\label{fig:crono}
\end{figure}
\end{landscape}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\renewcommand\bibname{BIBLIOGRAFÍA}
\bibliographystyle{IEEEtran}
%\bibliographystyle{apacite}
\addcontentsline{toc}{chapter}{BIBLIOGRAFÍA}
\bibliography{references}{}

%\def\bibindent{1cm}
%\begin{thebibliography}{99\kern\bibindent}%Si tienes más de 99 editar este número a la cantidad de referencias que tengas ;)
%\makeatletter
%\let\old@biblabel\@biblabel
%\def\@biblabel#1{{}\kern\bibindent} %En el { } agregar \old@biblabel{#1}
%\let\old@bibitem\bibitem
%\def\bibitem#1{\old@bibitem{#1}\leavevmode\kern-\bibindent\kern-2.1em}
%\makeatother
    

%    \bibitem{1} Law, A. M. y Kelton W. D. (2000). \textit{Simulation Modeling and Analysis} (3 edition). Boston, USA: McGraw-Hill.

%\end{thebibliography}



%\mainmatter %Esto hace que los anexos SI tengan números de cap.
%Se utilizan letras en vez de números.
\titleformat{\chapter}[hang]
{\fontsize{12}{12}\bfseries}
{ANEXO\hsp\Alph{chapter}{.}\hsp}{0pt}{\fontsize{12}{12}\bfseries}
\titlespacing{\chapter}{0cm}{-13.6pt}{0.21cm}[0cm]

%Se utilizan letras en vez de numeros.
\renewcommand{\thesection}{\Alph{chapter}.\arabic{section}}
\renewcommand{\thefigure}{\Alph{chapter}.\arabic{figure}}
\renewcommand{\thetable}{\Alph{chapter}.\arabic{table}}


%--------------------------------------------------------------------------
\end{document}